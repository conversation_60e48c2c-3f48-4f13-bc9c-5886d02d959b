"""Tests for VABS interface module."""

import numpy as np
import pytest
from pyvabs.vabs_interface import (
    VABSHomogenizationResult,
    VABSRecoveryResult,
    constitutive_modeling,
    recovery,
    validate_homogenization_inputs,
    validate_recovery_inputs,
)


class TestVABSHomogenizationResult:
    """Test cases for VABSHomogenizationResult class."""
    
    def test_init_default(self):
        """Test default initialization of homogenization result."""
        result = VABSHomogenizationResult()
        
        # Check that all properties are initialized to None or empty string
        assert result.mass is None
        assert result.area is None
        assert result.xm2 is None
        assert result.xm3 is None
        assert result.error == ""
    
    def test_mass_properties_assignment(self):
        """Test assignment of mass properties."""
        result = VABSHomogenizationResult()
        
        # Assign mass matrix
        mass_matrix = np.eye(6)
        result.mass = mass_matrix
        assert np.array_equal(result.mass, mass_matrix)
        
        # Assign scalar properties
        result.area = 1.5
        result.xm2 = 0.1
        result.xm3 = 0.2
        
        assert result.area == 1.5
        assert result.xm2 == 0.1
        assert result.xm3 == 0.2


class TestVABSRecoveryResult:
    """Test cases for VABSRecoveryResult class."""
    
    def test_init_default(self):
        """Test default initialization of recovery result."""
        result = VABSRecoveryResult()
        
        # Check that all properties are initialized to None or empty string
        assert result.disp_3D_F is None
        assert result.k_F is None
        assert result.ss_F is None
        assert result.error == ""
    
    def test_displacement_assignment(self):
        """Test assignment of displacement results."""
        result = VABSRecoveryResult()
        
        # Create sample displacement data
        nnode = 10
        disp_data = np.random.rand(nnode, 5)
        result.disp_3D_F = disp_data
        
        assert result.disp_3D_F.shape == (nnode, 5)
        assert np.array_equal(result.disp_3D_F, disp_data)


class TestConstitutiveModeling:
    """Test cases for constitutive_modeling function."""
    
    @pytest.fixture
    def sample_homogenization_inputs(self):
        """Fixture providing sample inputs for homogenization."""
        nnode = 4
        nelem = 2
        nmate = 1
        nlayer = 1
        
        return {
            'inp_name': 'test_input.dat',
            'format_I': 1,
            'mat_type_layer': np.array([1], dtype=np.int32),
            'layup_angle': np.array([0.0]),
            'LAY_CONST': 1,
            'nlayer': nlayer,
            'Timoshenko_I': 1,
            'curved_I': 0,
            'oblique_I': 0,
            'trapeze_I': 0,
            'Vlasov_I': 0,
            'damping_I': 0,
            'kb': np.array([0.0, 0.0, 0.0]),
            'beta': np.array([0.0, 0.0, 0.0]),
            'nnode': nnode,
            'nelem': nelem,
            'nmate': nmate,
            'coord': np.array([[0.0, 0.0], [1.0, 0.0], [1.0, 1.0], [0.0, 1.0]]),
            'element': np.array([[1, 2, 3, 4, 0, 0, 0, 0, 0],
                                [1, 2, 3, 4, 0, 0, 0, 0, 0]], dtype=np.int32),
            'layup': np.array([[0.0], [0.0]]),
            'mat_type': np.array([1, 1], dtype=np.int32),
            'material': np.zeros((nmate, 21)),
            'orth': np.array([1], dtype=np.int32),
            'density': np.array([1000.0]),
            'damping': np.array([0.0]),
            'damping_layer': np.array([0.0]),
        }
    
    def test_constitutive_modeling_basic(self, sample_homogenization_inputs):
        """Test basic call to constitutive_modeling function."""
        result = constitutive_modeling(**sample_homogenization_inputs)
        
        assert isinstance(result, VABSHomogenizationResult)
        # Currently returns "Not implemented yet" error
        assert "Not implemented" in result.error
    
    def test_constitutive_modeling_with_thermal(self, sample_homogenization_inputs):
        """Test constitutive_modeling with thermal analysis."""
        inputs = sample_homogenization_inputs.copy()
        inputs['thermal_I'] = 1
        inputs['cte'] = np.zeros((inputs['nmate'], 6))
        inputs['temperature'] = np.ones(inputs['nnode']) * 20.0
        
        result = constitutive_modeling(**inputs)
        
        assert isinstance(result, VABSHomogenizationResult)
        assert "Not implemented" in result.error
    
    def test_constitutive_modeling_input_validation(self):
        """Test input validation for constitutive_modeling."""
        # Test with minimal invalid inputs
        with pytest.raises(TypeError):
            # Missing required arguments should raise TypeError
            constitutive_modeling()


class TestRecovery:
    """Test cases for recovery function."""
    
    @pytest.fixture
    def sample_recovery_inputs(self):
        """Fixture providing sample inputs for recovery analysis."""
        nnode = 4
        nelem = 2
        nmate = 1
        nlayer = 1
        
        return {
            'inp_name': 'test_input.dat',
            'format_I': 1,
            'mat_type_layer': np.array([1], dtype=np.int32),
            'layup_angle': np.array([0.0]),
            'LAY_CONST': 1,
            'nlayer': nlayer,
            'recover_I': 2,  # Linear recovery
            'Timoshenko_I': 1,
            'curved_I': 0,
            'oblique_I': 0,
            'Vlasov_I': 0,
            'damping_I': 0,
            'kb': np.array([0.0, 0.0, 0.0]),
            'beta': np.array([0.0, 0.0, 0.0]),
            'nnode': nnode,
            'nelem': nelem,
            'nmate': nmate,
            'coord': np.array([[0.0, 0.0], [1.0, 0.0], [1.0, 1.0], [0.0, 1.0]]),
            'element': np.array([[1, 2, 3, 4, 0, 0, 0, 0, 0],
                                [1, 2, 3, 4, 0, 0, 0, 0, 0]], dtype=np.int32),
            'layup': np.array([[0.0], [0.0]]),
            'mat_type': np.array([1, 1], dtype=np.int32),
            'material': np.zeros((nmate, 21)),
            'orth': np.array([1], dtype=np.int32),
            'density': np.array([1000.0]),
            'damping': np.array([0.0]),
            'damping_layer': np.array([0.0]),
            'disp_1D': np.array([0.0, 0.0, 0.0]),
            'dir_cos_1D': np.eye(3),
            'strain_CL': np.zeros(4),
            'strain_CL_1': np.zeros(4),
            'strain_CL_2': np.zeros(4),
            'force_1D': np.array([0.0, 0.0, 1000.0, 0.0, 0.0, 0.0]),
            'load_1D': np.zeros(6),
            'load1_1D': np.zeros(6),
            'load2_1D': np.zeros(6),
        }
    
    def test_recovery_basic(self, sample_recovery_inputs):
        """Test basic call to recovery function."""
        result = recovery(**sample_recovery_inputs)
        
        assert isinstance(result, VABSRecoveryResult)
        # Currently returns "Not implemented yet" error
        assert "Not implemented" in result.error
    
    def test_recovery_with_failure_analysis(self, sample_recovery_inputs):
        """Test recovery with failure analysis."""
        inputs = sample_recovery_inputs.copy()
        inputs['recover_I'] = 3  # Failure analysis
        inputs['fc'] = np.array([[1, 5]], dtype=np.int32)  # Failure criterion 1, 5 constants
        inputs['strength_constants'] = np.ones((inputs['nmate'], 9))
        
        result = recovery(**inputs)
        
        assert isinstance(result, VABSRecoveryResult)
        assert "Not implemented" in result.error
    
    def test_recovery_with_thermal(self, sample_recovery_inputs):
        """Test recovery with thermal effects."""
        inputs = sample_recovery_inputs.copy()
        inputs['thermal_I'] = 1
        inputs['cte'] = np.zeros((inputs['nmate'], 6))
        inputs['temperature'] = np.ones(inputs['nnode']) * 20.0
        
        result = recovery(**inputs)
        
        assert isinstance(result, VABSRecoveryResult)
        assert "Not implemented" in result.error


class TestValidationFunctions:
    """Test cases for input validation functions."""
    
    def test_validate_homogenization_inputs_valid(self):
        """Test validation with valid homogenization inputs."""
        nnode, nelem, nmate, nlayer = 4, 2, 1, 1
        coord = np.zeros((nnode, 2))
        element = np.zeros((nelem, 9), dtype=np.int32)
        material = np.zeros((nmate, 21))
        
        # Should not raise any exception
        validate_homogenization_inputs(
            nnode=nnode,
            nelem=nelem,
            nmate=nmate,
            nlayer=nlayer,
            coord=coord,
            element=element,
            material=material
        )
    
    def test_validate_homogenization_inputs_invalid_counts(self):
        """Test validation with invalid node/element counts."""
        coord = np.zeros((4, 2))
        element = np.zeros((2, 9), dtype=np.int32)
        material = np.zeros((1, 21))
        
        with pytest.raises(ValueError, match="Number of nodes must be positive"):
            validate_homogenization_inputs(
                nnode=0, nelem=2, nmate=1, nlayer=1,
                coord=coord, element=element, material=material
            )
        
        with pytest.raises(ValueError, match="Number of elements must be positive"):
            validate_homogenization_inputs(
                nnode=4, nelem=-1, nmate=1, nlayer=1,
                coord=coord, element=element, material=material
            )
    
    def test_validate_homogenization_inputs_wrong_shapes(self):
        """Test validation with wrong array shapes."""
        nnode, nelem, nmate, nlayer = 4, 2, 1, 1
        
        # Wrong coord shape
        coord = np.zeros((nnode, 3))  # Should be (nnode, 2)
        element = np.zeros((nelem, 9), dtype=np.int32)
        material = np.zeros((nmate, 21))
        
        with pytest.raises(ValueError, match="coord must have shape"):
            validate_homogenization_inputs(
                nnode=nnode, nelem=nelem, nmate=nmate, nlayer=nlayer,
                coord=coord, element=element, material=material
            )
        
        # Wrong material shape
        coord = np.zeros((nnode, 2))
        material = np.zeros((nmate, 20))  # Should be (nmate, 21)
        
        with pytest.raises(ValueError, match="material must have shape"):
            validate_homogenization_inputs(
                nnode=nnode, nelem=nelem, nmate=nmate, nlayer=nlayer,
                coord=coord, element=element, material=material
            )
    
    def test_validate_recovery_inputs_valid(self):
        """Test validation with valid recovery inputs."""
        nnode, nelem, nmate, nlayer = 4, 2, 1, 1
        coord = np.zeros((nnode, 2))
        element = np.zeros((nelem, 9), dtype=np.int32)
        material = np.zeros((nmate, 21))
        disp_1D = np.zeros(3)
        force_1D = np.zeros(6)
        
        # Should not raise any exception
        validate_recovery_inputs(
            nnode=nnode, nelem=nelem, nmate=nmate, nlayer=nlayer,
            recover_I=2, disp_1D=disp_1D, force_1D=force_1D,
            coord=coord, element=element, material=material
        )
    
    def test_validate_recovery_inputs_invalid_recover_flag(self):
        """Test validation with invalid recovery flag."""
        nnode, nelem, nmate, nlayer = 4, 2, 1, 1
        coord = np.zeros((nnode, 2))
        element = np.zeros((nelem, 9), dtype=np.int32)
        material = np.zeros((nmate, 21))
        disp_1D = np.zeros(3)
        force_1D = np.zeros(6)
        
        with pytest.raises(ValueError, match="recover_I must be"):
            validate_recovery_inputs(
                nnode=nnode, nelem=nelem, nmate=nmate, nlayer=nlayer,
                recover_I=4, disp_1D=disp_1D, force_1D=force_1D,
                coord=coord, element=element, material=material
            )
    
    def test_validate_recovery_inputs_wrong_array_shapes(self):
        """Test validation with wrong array shapes for recovery-specific inputs."""
        nnode, nelem, nmate, nlayer = 4, 2, 1, 1
        coord = np.zeros((nnode, 2))
        element = np.zeros((nelem, 9), dtype=np.int32)
        material = np.zeros((nmate, 21))
        
        # Wrong disp_1D shape
        disp_1D = np.zeros(4)  # Should be (3,)
        force_1D = np.zeros(6)
        
        with pytest.raises(ValueError, match="disp_1D must have shape"):
            validate_recovery_inputs(
                nnode=nnode, nelem=nelem, nmate=nmate, nlayer=nlayer,
                recover_I=2, disp_1D=disp_1D, force_1D=force_1D,
                coord=coord, element=element, material=material
            )
        
        # Wrong force_1D shape
        disp_1D = np.zeros(3)
        force_1D = np.zeros(5)  # Should be (6,)
        
        with pytest.raises(ValueError, match="force_1D must have shape"):
            validate_recovery_inputs(
                nnode=nnode, nelem=nelem, nmate=nmate, nlayer=nlayer,
                recover_I=2, disp_1D=disp_1D, force_1D=force_1D,
                coord=coord, element=element, material=material
            )


class TestHelperFunctions:
    """Test cases for VABS helper functions."""

    def test_create_material_matrix_isotropic(self):
        """Test creation of isotropic material matrix."""
        from pyvabs.vabs_interface import create_material_matrix

        material = create_material_matrix(
            material_type='isotropic',
            E1=200e9,  # 200 GPa
            nu12=0.3
        )

        assert material.shape == (21,)
        assert material[0] == 200e9  # Young's modulus
        assert material[1] == 0.3    # Poisson's ratio
        assert np.all(material[2:] == 0)  # Other entries should be zero

    def test_create_material_matrix_orthotropic(self):
        """Test creation of orthotropic material matrix."""
        from pyvabs.vabs_interface import create_material_matrix

        material = create_material_matrix(
            material_type='orthotropic',
            E1=150e9, E2=10e9, E3=10e9,
            G12=5e9, G13=5e9, G23=3e9,
            nu12=0.3, nu13=0.3, nu23=0.4
        )

        assert material.shape == (21,)
        assert material[0] == 150e9  # E1
        assert material[1] == 10e9   # E2
        assert material[2] == 10e9   # E3
        assert material[3] == 5e9    # G12
        assert material[6] == 0.3    # nu12

    def test_create_material_matrix_invalid_type(self):
        """Test creation with invalid material type."""
        from pyvabs.vabs_interface import create_material_matrix

        with pytest.raises(ValueError, match="Unsupported material type"):
            create_material_matrix(material_type='invalid')

    def test_create_material_matrix_invalid_properties(self):
        """Test creation with invalid material properties."""
        from pyvabs.vabs_interface import create_material_matrix

        with pytest.raises(ValueError, match="Invalid isotropic material properties"):
            create_material_matrix(material_type='isotropic', E1=-100, nu12=0.3)

        with pytest.raises(ValueError, match="Invalid orthotropic material properties"):
            create_material_matrix(
                material_type='orthotropic',
                E1=0, E2=10e9, E3=10e9,  # E1 = 0 is invalid
                G12=5e9, G13=5e9, G23=3e9
            )

    def test_create_simple_mesh(self):
        """Test creation of simple rectangular mesh."""
        from pyvabs.vabs_interface import create_simple_mesh

        width, height = 2.0, 1.0
        nx, ny = 2, 1

        coords, elements = create_simple_mesh(width, height, nx, ny)

        # Check coordinates
        expected_nnode = (nx + 1) * (ny + 1)  # 3 * 2 = 6 nodes
        assert coords.shape == (expected_nnode, 2)

        # Check that coordinates span the correct range
        assert np.min(coords[:, 0]) == 0.0
        assert np.max(coords[:, 0]) == width
        assert np.min(coords[:, 1]) == 0.0
        assert np.max(coords[:, 1]) == height

        # Check elements
        expected_nelem = nx * ny  # 2 * 1 = 2 elements
        assert elements.shape == (expected_nelem, 9)

        # Check that element connectivity uses 1-based indexing
        assert np.min(elements[elements > 0]) == 1
        assert np.max(elements[elements > 0]) == expected_nnode

    def test_create_simple_mesh_single_element(self):
        """Test creation of single element mesh."""
        from pyvabs.vabs_interface import create_simple_mesh

        coords, elements = create_simple_mesh(1.0, 1.0, 1, 1)

        # Should have 4 nodes for single quadrilateral element
        assert coords.shape == (4, 2)
        assert elements.shape == (1, 9)

        # Check corner coordinates
        expected_coords = np.array([[0, 0], [1, 0], [1, 1], [0, 1]])
        np.testing.assert_array_equal(coords, expected_coords)

        # Check element connectivity (1-based)
        expected_element = np.array([1, 2, 3, 4, 0, 0, 0, 0, 0])
        np.testing.assert_array_equal(elements[0], expected_element)

    def test_parse_vabs_output_stress_strain_beam_coords(self):
        """Test parsing of VABS stress/strain output in beam coordinates."""
        from pyvabs.vabs_interface import parse_vabs_output_stress_strain

        # Create sample stress/strain data (n=2, 26 columns)
        ss_data = np.random.rand(2, 26)

        result = parse_vabs_output_stress_strain(ss_data, 'beam')

        assert 'position' in result
        assert 'strain' in result
        assert 'stress' in result

        assert result['position'].shape == (2, 2)  # x2, x3 positions
        assert result['strain'].shape == (2, 6)    # 6 strain components
        assert result['stress'].shape == (2, 6)    # 6 stress components

        # Check that correct columns are extracted
        np.testing.assert_array_equal(result['position'], ss_data[:, :2])
        np.testing.assert_array_equal(result['strain'], ss_data[:, 2:8])
        np.testing.assert_array_equal(result['stress'], ss_data[:, 8:14])

    def test_parse_vabs_output_stress_strain_material_coords(self):
        """Test parsing of VABS stress/strain output in material coordinates."""
        from pyvabs.vabs_interface import parse_vabs_output_stress_strain

        # Create sample stress/strain data
        ss_data = np.random.rand(3, 26)

        result = parse_vabs_output_stress_strain(ss_data, 'material')

        assert result['position'].shape == (3, 2)
        assert result['strain'].shape == (3, 6)
        assert result['stress'].shape == (3, 6)

        # Check that correct columns are extracted for material coordinates
        np.testing.assert_array_equal(result['strain'], ss_data[:, 14:20])
        np.testing.assert_array_equal(result['stress'], ss_data[:, 20:26])

    def test_parse_vabs_output_invalid_coordinate_system(self):
        """Test parsing with invalid coordinate system."""
        from pyvabs.vabs_interface import parse_vabs_output_stress_strain

        ss_data = np.random.rand(2, 26)

        with pytest.raises(ValueError, match="coordinate_system must be"):
            parse_vabs_output_stress_strain(ss_data, 'invalid')


class TestVABSConstants:
    """Test cases for VABS constants."""

    def test_vabs_constants(self):
        """Test that VABS constants are properly defined."""
        from pyvabs.vabs_interface import DBL, TOLERANCE, NDIM, MAX_NODE_ELEM, NE_ID

        assert DBL == 8
        assert TOLERANCE == 1e-12
        assert NDIM == 2
        assert MAX_NODE_ELEM == 9
        assert NE_ID == 4
