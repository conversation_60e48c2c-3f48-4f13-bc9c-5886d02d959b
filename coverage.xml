<?xml version="1.0" ?>
<coverage version="7.10.2" timestamp="1754270848855" lines-valid="52" lines-covered="49" line-rate="0.9423" branches-covered="0" branches-valid="0" branch-rate="0" complexity="0">
	<!-- Generated by coverage.py: https://coverage.readthedocs.io/en/7.10.2 -->
	<!-- Based on https://raw.githubusercontent.com/cobertura/web/master/htdocs/xml/coverage-04.dtd -->
	<sources>
		<source>C:\Users\<USER>\work\pyvabs\src\pyvabs</source>
	</sources>
	<packages>
		<package name="." line-rate="0.9423" branch-rate="0" complexity="0">
			<classes>
				<class name="__init__.py" filename="__init__.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="6" hits="1"/>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="11" hits="1"/>
						<line number="12" hits="1"/>
						<line number="15" hits="1"/>
						<line number="21" hits="1"/>
						<line number="24" hits="1"/>
					</lines>
				</class>
				<class name="core.py" filename="core.py" complexity="0" line-rate="1" branch-rate="0">
					<methods/>
					<lines>
						<line number="7" hits="1"/>
						<line number="10" hits="1"/>
						<line number="20" hits="1"/>
						<line number="27" hits="1"/>
						<line number="28" hits="1"/>
						<line number="30" hits="1"/>
						<line number="40" hits="1"/>
						<line number="42" hits="1"/>
						<line number="48" hits="1"/>
						<line number="55" hits="1"/>
						<line number="65" hits="1"/>
					</lines>
				</class>
				<class name="utils.py" filename="utils.py" complexity="0" line-rate="0.9091" branch-rate="0">
					<methods/>
					<lines>
						<line number="7" hits="1"/>
						<line number="8" hits="1"/>
						<line number="9" hits="1"/>
						<line number="10" hits="1"/>
						<line number="13" hits="1"/>
						<line number="23" hits="1"/>
						<line number="24" hits="1"/>
						<line number="27" hits="1"/>
						<line number="32" hits="1"/>
						<line number="33" hits="1"/>
						<line number="34" hits="1"/>
						<line number="37" hits="1"/>
						<line number="38" hits="0"/>
						<line number="39" hits="0"/>
						<line number="40" hits="0"/>
						<line number="42" hits="1"/>
						<line number="45" hits="1"/>
						<line number="58" hits="1"/>
						<line number="60" hits="1"/>
						<line number="61" hits="1"/>
						<line number="63" hits="1"/>
						<line number="64" hits="1"/>
						<line number="67" hits="1"/>
						<line number="74" hits="1"/>
						<line number="75" hits="1"/>
						<line number="77" hits="1"/>
						<line number="78" hits="1"/>
						<line number="81" hits="1"/>
						<line number="91" hits="1"/>
						<line number="94" hits="1"/>
						<line number="103" hits="1"/>
						<line number="106" hits="1"/>
						<line number="116" hits="1"/>
					</lines>
				</class>
			</classes>
		</package>
	</packages>
</coverage>
