{"pdf_info": [{"para_blocks": [{"bbox": [222, 144, 394, 162], "type": "title", "lines": [{"bbox": [222, 144, 394, 162], "spans": [{"bbox": [222, 144, 394, 162], "type": "text", "content": "VABS Manual for Users*"}]}], "index": 0, "level": 1, "line_avg_height": 21}, {"bbox": [267, 202, 344, 217], "type": "text", "lines": [{"bbox": [267, 202, 344, 217], "spans": [{"bbox": [267, 202, 344, 217], "type": "text", "content": "August 1, 2024"}]}], "index": 1}, {"bbox": [72, 280, 185, 297], "type": "title", "lines": [{"bbox": [72, 280, 185, 297], "spans": [{"bbox": [72, 280, 185, 297], "type": "text", "content": "1 Introduction"}]}], "index": 2, "level": 2, "line_avg_height": 19}, {"bbox": [70, 307, 540, 361], "type": "text", "lines": [{"bbox": [70, 307, 540, 361], "spans": [{"bbox": [70, 307, 540, 361], "type": "text", "content": "VABS (Variational Asymptotic Beam Sectional Analysis) is a code implementing the beam theo- . ries "}, {"bbox": [70, 307, 540, 361], "type": "inline_equation", "content": "1 - 13,13 - 24"}, {"bbox": [70, 307, 540, 361], "type": "text", "content": " based on the concept of simplifying the original nonlinear three- dimensional (3D) analysis of slender structures into a two- dimensional (2D) cross- sectional analysis and a one- dimensional (1D) nonlinear beam analysis using the variational asymptotic method25"}]}], "index": 3}, {"bbox": [70, 375, 540, 456], "type": "text", "lines": [{"bbox": [70, 375, 540, 456], "spans": [{"bbox": [70, 375, 540, 456], "type": "text", "content": "VABS takes a finite element mesh of the cross- section including all the details of geometry and material as inputs to perform a homogenization analysis to compute sectional properties including inertial properties and structural properties. These properties are needed for the 1D beam analysis to predict the global behavior of slender structures. VABS can also perform a dehomogenization analysis to compute the distributions of 3D displacements/strains/stresses, and failure indexes and strength ratios over the cross- section based on the global behavior of the 1D beam analysis."}]}], "index": 4}, {"bbox": [70, 470, 540, 524], "type": "text", "lines": [{"bbox": [70, 470, 540, 524], "spans": [{"bbox": [70, 470, 540, 524], "type": "text", "content": "Since most of the theoretical details are presented in pertinent papers and collected in the book by Prof<PERSON>, this manual will only serve to help readers get started using VABS to solve their own composite beam problems. This manual addresses the history of the code, its features, functionalities, conventions, inputs, outputs, maintenance, and tech support."}]}], "index": 5}, {"bbox": [71, 541, 198, 559], "type": "title", "lines": [{"bbox": [71, 541, 198, 559], "spans": [{"bbox": [71, 541, 198, 559], "type": "text", "content": "2 VABS History"}]}], "index": 6, "level": 2, "line_avg_height": 23}, {"bbox": [70, 569, 540, 624], "type": "text", "lines": [{"bbox": [70, 569, 540, 624], "spans": [{"bbox": [70, 569, 540, 624], "type": "text", "content": "The research project that gave birth to VABS was initiated by Prof. <PERSON> when he was first introduced to the variational asymptotic method by Prof<PERSON> at Georgia Tech in 1989 and has been ongoing ever since till the time of writing. The program name VABS first appeared in [1]."}]}], "index": 7}, {"bbox": [70, 637, 540, 664], "type": "text", "lines": [{"bbox": [70, 637, 540, 664], "spans": [{"bbox": [70, 637, 540, 664], "type": "text", "content": "The original version of VABS was based on a research code written by Prof. <PERSON><PERSON><PERSON>. The fall semester of 1998, when Prof<PERSON> began his graduate study at Georgia Tech, marked the beginning"}]}], "index": 8}], "discarded_blocks": [], "page_size": [612, 792], "page_idx": 0}, {"para_blocks": [{"bbox": [70, 104, 540, 240], "type": "text", "lines": [{"bbox": [70, 104, 540, 240], "spans": [{"bbox": [70, 104, 540, 240], "type": "text", "content": "of the transition of VABS from a research code to a production design and analysis tool for practicing engineers. The code was rewritten from scratch using the modern Fortran language, with all unnecessary restrictions eliminated, and the computing and memory efficiency greatly enhanced. At the same time, Prof<PERSON> was continuing his work on VABS for piezoelectric materials at MIT and later at University of Michigan. <PERSON><PERSON><PERSON> and <PERSON> continue their work on VABS for multiphysics modeling at Georgia Tech, Utah State University, and Purdue University. For this reason, there are two variants of VABS: the Georgia Tech/Utah State/Purdue VABS, released and maintained by <PERSON><PERSON>, and UM/VABS, released and maintained by Prof<PERSON>. From henceforth in this manual the term VABS will refer only to the Georgia Tech/Utah State/Purdue VABS, and what follows is only applicable to this code."}]}], "index": 0}, {"bbox": [70, 254, 540, 308], "type": "text", "lines": [{"bbox": [70, 254, 540, 308], "spans": [{"bbox": [70, 254, 540, 308], "type": "text", "content": "Many researchers and engineers all over the world are actively using VABS. VABS is becoming a standard tool for design and analysis of composite slender structures such as helicopter rotor blades, wind turbine blades, high aspect ratio wings, UAM/eVTOL/UAV blades, propellers, landing gear, composite tubes, etc."}]}], "index": 1}, {"bbox": [70, 326, 386, 342], "type": "title", "lines": [{"bbox": [70, 326, 386, 342], "spans": [{"bbox": [70, 326, 386, 342], "type": "text", "content": "3 What is New in Different VABS versions"}]}], "index": 2, "level": 2, "line_avg_height": 22}, {"bbox": [72, 352, 258, 367], "type": "title", "lines": [{"bbox": [72, 352, 258, 367], "spans": [{"bbox": [72, 352, 258, 367], "type": "text", "content": "3.1 What is New in VABS 4.1"}]}], "index": 3, "level": 3, "line_avg_height": 17}, {"bbox": [72, 374, 255, 388], "type": "text", "lines": [{"bbox": [72, 374, 255, 388], "spans": [{"bbox": [72, 374, 255, 388], "type": "text", "content": "The new capabilities of VABS 4.1 are:"}]}], "index": 4}, {"bbox": [81, 396, 550, 667], "type": "text", "lines": [{"bbox": [81, 396, 550, 667], "spans": [{"bbox": [81, 396, 550, 667], "type": "text", "content": "1. Perform dehomogenization for multiple load cases in terms of forces and moments corresponding to the <PERSON><PERSON><PERSON><PERSON> model and the <PERSON><PERSON><PERSON> model.  \n2. Output the modes and corresponding ratios for failure criteria with identifiable modes such as failure criterion 1, 2, 5.  \n3. Provide documentation for the user-define failure criterion capability.  \n4. Enable users to suppress the output of other stress/strain files except the average stresses/strains for each element in the ele file.  \n5. Remove damping input for no damping analysis.  \n6. Output the classical stiffness matrix and compliance matrix at the shear center.  \n7. Enable comment lines in the inputs for better readability.  \n8. Output the time and date the code is compiled and released.  \n9. Provide simple instructions when VABS, VABS -h, or VABS -help is issued in the command line.  \n10. Simplify the installation process without administrator privilege."}]}], "index": 5}], "discarded_blocks": [], "page_size": [612, 792], "page_idx": 1}, {"para_blocks": [{"bbox": [70, 103, 258, 118], "type": "title", "lines": [{"bbox": [70, 103, 258, 118], "spans": [{"bbox": [70, 103, 258, 118], "type": "text", "content": "3.2 What is New in VABS 4.0"}]}], "index": 0, "level": 3, "line_avg_height": 17}, {"bbox": [70, 125, 255, 138], "type": "text", "lines": [{"bbox": [70, 125, 255, 138], "spans": [{"bbox": [70, 125, 255, 138], "type": "text", "content": "The new capabilities of VABS 4.0 are:"}]}], "index": 1}, {"bbox": [81, 147, 542, 333], "type": "text", "lines": [{"bbox": [81, 147, 542, 333], "spans": [{"bbox": [81, 147, 542, 333], "type": "text", "content": "1. Compute pointwise distributions of failure indexes and strength ratios over the cross-section under a given load. \n2. Compute the safety margin (the minimum strength ratio among all elements) of the crosssection under a given load. \n3. Output the nodal stress/strain values according to the original numbering of the finite element nodes. \n4. Output the complete set of engineering properties commonly used in conventional beam analysis including extension stiffness "}, {"bbox": [81, 147, 542, 333], "type": "inline_equation", "content": "(EA)"}, {"bbox": [81, 147, 542, 333], "type": "text", "content": " , torsional stiffness "}, {"bbox": [81, 147, 542, 333], "type": "inline_equation", "content": "(GJ)"}, {"bbox": [81, 147, 542, 333], "type": "text", "content": " , principal bending stiffnesses "}, {"bbox": [81, 147, 542, 333], "type": "inline_equation", "content": "(EI_{22},EI_{33})"}, {"bbox": [81, 147, 542, 333], "type": "text", "content": " , principal shear stiffness "}, {"bbox": [81, 147, 542, 333], "type": "inline_equation", "content": "(GA_{22},GA_{33})"}, {"bbox": [81, 147, 542, 333], "type": "text", "content": " , tension center, shear center, principal inertial axes, principal bending axes, and principal shear axes. \n5. Since VABS 4.0, we change the executable name to VABS."}]}], "index": 2}, {"bbox": [70, 346, 259, 361], "type": "title", "lines": [{"bbox": [70, 346, 259, 361], "spans": [{"bbox": [70, 346, 259, 361], "type": "text", "content": "3.3 What is New in VABS 3.9"}]}], "index": 3, "level": 3, "line_avg_height": 17}, {"bbox": [70, 369, 541, 409], "type": "text", "lines": [{"bbox": [70, 369, 541, 409], "spans": [{"bbox": [70, 369, 541, 409], "type": "text", "content": "The main new feature of VABS 3.9 is prediction of sectional damping matrix based on the lamina damping coefficients. VABS 3.9 can also output the cross- sectional area. Another feature is that VABS 3.9 is now distributed as a single library."}]}], "index": 4}, {"bbox": [70, 424, 258, 438], "type": "title", "lines": [{"bbox": [70, 424, 258, 438], "spans": [{"bbox": [70, 424, 258, 438], "type": "text", "content": "3.4 What is New in VABS 3.8"}]}], "index": 5, "level": 3, "line_avg_height": 17}, {"bbox": [70, 445, 541, 513], "type": "text", "lines": [{"bbox": [70, 445, 541, 513], "spans": [{"bbox": [70, 445, 541, 513], "type": "text", "content": "The main new feature of VABS 3.8 is a new license manager to allow more versatile license mechanisms including node locked licenses and floating licenses. The user does not have to put the license in the same folder as the input file as it was the case for previous versions. Instead, the user can use a node locked license file stored in the same folder as the VABS executable or obtain a floating license from a license server."}]}], "index": 6}, {"bbox": [70, 527, 541, 554], "type": "text", "lines": [{"bbox": [70, 527, 541, 554], "spans": [{"bbox": [70, 527, 541, 554], "type": "text", "content": "Since VABS 3.8, we provide free academic licenses for students and professors to use the full version of VABS for teaching and academic research."}]}], "index": 7}, {"bbox": [86, 567, 391, 581], "type": "text", "lines": [{"bbox": [86, 567, 391, 581], "spans": [{"bbox": [86, 567, 391, 581], "type": "text", "content": "Since VABS 3.8, we provide both Linux and Windows versions."}]}], "index": 8}, {"bbox": [70, 596, 258, 610], "type": "title", "lines": [{"bbox": [70, 596, 258, 610], "spans": [{"bbox": [70, 596, 258, 610], "type": "text", "content": "3.5 What is New in VABS 3.7"}]}], "index": 9, "level": 3, "line_avg_height": 17}, {"bbox": [70, 617, 541, 644], "type": "text", "lines": [{"bbox": [70, 617, 541, 644], "spans": [{"bbox": [70, 617, 541, 644], "type": "text", "content": "The main new feature of VABS 3.7 is to carry out the recovery up to the second order which provides a better prediction for the 3D recovered fields in comparison to known exact solutions."}]}], "index": 10}], "discarded_blocks": [], "page_size": [612, 792], "page_idx": 2}, {"para_blocks": [{"bbox": [71, 103, 258, 118], "type": "title", "lines": [{"bbox": [71, 103, 258, 118], "spans": [{"bbox": [71, 103, 258, 118], "type": "text", "content": "3.6 What is New in VABS 3.6"}]}], "index": 0, "level": 3, "line_avg_height": 16}, {"bbox": [70, 125, 540, 179], "type": "text", "lines": [{"bbox": [70, 125, 540, 179], "spans": [{"bbox": [70, 125, 540, 179], "type": "text", "content": "3.6 What is New in VABS 3.6The main new feature of VABS 3.6 is to use an improved method for optimizing the numbering of finite element mesh. For large problems, it is much faster than the method used in previous versions. The most recent version of fortran compiler is used for compiling the code resulting in much faster recovery."}]}], "index": 1}, {"bbox": [71, 194, 258, 208], "type": "title", "lines": [{"bbox": [71, 194, 258, 208], "spans": [{"bbox": [71, 194, 258, 208], "type": "text", "content": "3.7 What is New in VABS 3.5"}]}], "index": 2, "level": 3, "line_avg_height": 17}, {"bbox": [70, 217, 540, 270], "type": "text", "lines": [{"bbox": [70, 217, 540, 270], "spans": [{"bbox": [70, 217, 540, 270], "type": "text", "content": "3.7 What is New in VABS 3.5The main new feature of VABS 3.5 is for oblique cross- sectional analysis, the inputs are given in the oblique cross- sectional system while in previous versions, the inputs are given in the normal cross- sectional coordinates. Also starting VABS 3.5, users can use long names of input file, including spaces in the path and file names."}]}], "index": 3}, {"bbox": [71, 285, 258, 299], "type": "title", "lines": [{"bbox": [71, 285, 258, 299], "spans": [{"bbox": [71, 285, 258, 299], "type": "text", "content": "3.8 What is New in VABS 3.4"}]}], "index": 4, "level": 3, "line_avg_height": 17}, {"bbox": [70, 306, 540, 373], "type": "text", "lines": [{"bbox": [70, 306, 540, 373], "spans": [{"bbox": [70, 306, 540, 373], "type": "text", "content": "3.8 What is New in VABS 3.4The main new feature is expanding "}, {"bbox": [70, 306, 540, 373], "type": "inline_equation", "content": "\\sqrt{g}"}, {"bbox": [70, 306, 540, 373], "type": "text", "content": " in the modeling for initially curved/twisted beams. For some cases such a change made significant differences for obtaining first and second correction of the stiffness matrix due to initial curvature and twist. Such a change is verified using an initially curved strip for which an elasticity solution is obtainable. The input file for this case is isorrectTrif2. sg. A 64- bit version of VABS is also available since VABS 3.4."}]}], "index": 5}, {"bbox": [71, 388, 258, 403], "type": "title", "lines": [{"bbox": [71, 388, 258, 403], "spans": [{"bbox": [71, 388, 258, 403], "type": "text", "content": "3.9 What is New in VABS 3.3"}]}], "index": 6, "level": 3, "line_avg_height": 18}, {"bbox": [70, 411, 540, 545], "type": "text", "lines": [{"bbox": [70, 411, 540, 545], "spans": [{"bbox": [70, 411, 540, 545], "type": "text", "content": "3.9 What is New in VABS 3.3The main new feature is introducing a new input format, and keeping the previous input format as optional. In the new format, the user only needs to provide one real number for "}, {"bbox": [70, 411, 540, 545], "type": "inline_equation", "content": "\\theta_{1}"}, {"bbox": [70, 411, 540, 545], "type": "text", "content": " as few users take advantage of the nine real numbers for "}, {"bbox": [70, 411, 540, 545], "type": "inline_equation", "content": "\\theta_{1}"}, {"bbox": [70, 411, 540, 545], "type": "text", "content": ", which is useful for elements with highly curved edges. In the new format, we introduce layer definition so that a layer type instead of material type is provided for each element. Each layer is defined through a unique combination of material type and layerup angle "}, {"bbox": [70, 411, 540, 545], "type": "inline_equation", "content": "\\theta_{3}"}, {"bbox": [70, 411, 540, 545], "type": "text", "content": ". It is more economical than assigning "}, {"bbox": [70, 411, 540, 545], "type": "inline_equation", "content": "\\theta_{3}"}, {"bbox": [70, 411, 540, 545], "type": "text", "content": " for each element, as what we have done in the previous format, because the number of layers usually is much less than the number of elements. These two changes reduce approximately "}, {"bbox": [70, 411, 540, 545], "type": "inline_equation", "content": "3 / 4"}, {"bbox": [70, 411, 540, 545], "type": "text", "content": " of real numbers needed for VABS inputs, saving space and time. These changes will also simplify the development of VABS preprocessors as it is easier to compute just one number for "}, {"bbox": [70, 411, 540, 545], "type": "inline_equation", "content": "\\theta_{1}"}, {"bbox": [70, 411, 540, 545], "type": "text", "content": " for each element."}]}], "index": 7}, {"bbox": [70, 560, 274, 574], "type": "title", "lines": [{"bbox": [70, 560, 274, 574], "spans": [{"bbox": [70, 560, 274, 574], "type": "text", "content": "3.10 VABS III and What is New"}]}], "index": 8, "level": 3, "line_avg_height": 18}, {"bbox": [70, 582, 553, 636], "type": "text", "lines": [{"bbox": [70, 582, 553, 636], "spans": [{"bbox": [70, 582, 553, 636], "type": "text", "content": "VABS was originally designed to run as a standalone code and its error handling, memory allocation/deallocation, and I/O were handled with this use in mind. However, in recent years, more and more users began to explore the possibility of using VABS in a design environment. This motivates the major upgrade of VABS to VABS III through restructuring the code."}]}], "index": 9}, {"bbox": [70, 650, 540, 704], "type": "text", "lines": [{"bbox": [70, 650, 540, 704], "spans": [{"bbox": [70, 650, 540, 704], "type": "text", "content": "Since the first release of VABS III, a few users have asked the difference between VABS III and previous versions, in particular VABS 2.1.1 which was the last release and the code accompanying <PERSON><PERSON>' book? Overall, VABS III is a much improved code in both accuracy and efficiency. The main difference can be described according to the following two aspects."}]}], "index": 10}], "discarded_blocks": [], "page_size": [612, 792], "page_idx": 3}, {"para_blocks": [{"bbox": [88, 105, 315, 118], "type": "text", "lines": [{"bbox": [88, 105, 315, 118], "spans": [{"bbox": [88, 105, 315, 118], "type": "text", "content": "- As far as functionalities concerned, VABS III"}]}], "index": 0}, {"bbox": [105, 126, 541, 193], "type": "text", "lines": [{"bbox": [105, 126, 541, 193], "spans": [{"bbox": [105, 126, 541, 193], "type": "text", "content": "1. Uses the correct constraints so that it can reproduce the 3D elasticity theory for isotropic prismatic beams. This change affects the warping functions, and affects all stiffness models except the classical one. Such a correction enables VABS to reproduce the 3D elasticity theory for isotropic prismatic beams and thus enables VABS to provide a better modeling for prismatic or initially curved/twisted composite beams (VABS 3.0)."}]}], "index": 1}, {"bbox": [107, 196, 541, 277], "type": "text", "lines": [{"bbox": [107, 196, 541, 277], "spans": [{"bbox": [107, 196, 541, 277], "type": "text", "content": "2. Recovers 3D stress/strain fields at each node in addition to Gauss points. The recovered 3D stress/strain fields are expressed in both the beam coordinate system and the material coordinate system. VABS 2.1.1 only recovers 3D stress/strain fields at Gauss points expressed in the beam coordinate system. For visualization, nodal values are convenient. To apply failure criteria of composite materials, stresses/strains expressed in the material coordinate system are needed (VABS 3.0)."}]}], "index": 2}, {"bbox": [107, 280, 540, 334], "type": "text", "lines": [{"bbox": [107, 280, 540, 334], "spans": [{"bbox": [107, 280, 540, 334], "type": "text", "content": "3. Handles isotropic, orthotropic, and anisotropic material differently. Previous versions treat all materials as orthotropic only and must take a total of 9 elastic constants. VABS III allows general anisotropic material with as many as 21 elastic constants and isotropic materials with as few as 2 elastic constants (VABS 3.0)."}]}], "index": 3}, {"bbox": [107, 337, 540, 391], "type": "text", "lines": [{"bbox": [107, 337, 540, 391], "spans": [{"bbox": [107, 337, 540, 391], "type": "text", "content": "4. Can model hygrothermal effects of composite beams due to temperature and moisture changes. As a companion capability, VABS Conduction is developed to carry out a dimensional reduction for the 3D conduction problem. VABS Conduction can be requested separately (VABS 3.1)."}]}], "index": 4}, {"bbox": [107, 393, 540, 447], "type": "text", "lines": [{"bbox": [107, 393, 540, 447], "spans": [{"bbox": [107, 393, 540, 447], "type": "text", "content": "5. Updates the transformation procedure into the <PERSON><PERSON><PERSON> model from the asymptotic energy. A new perturbation method is developed to capture the effects due to initial curvatures/twist during the transformation. The prediction for Timoshenko stiffness is generally improved, even for some prismatic beams (VABS 3.2)."}]}], "index": 5}, {"bbox": [105, 450, 539, 477], "type": "text", "lines": [{"bbox": [105, 450, 539, 477], "spans": [{"bbox": [105, 450, 539, 477], "type": "text", "content": "6. Outputs the average of 3D stresses/strains within each element for convenience of postprocessing (VABS 3.2.2)."}]}], "index": 6}, {"bbox": [105, 479, 540, 507], "type": "text", "lines": [{"bbox": [105, 479, 540, 507], "spans": [{"bbox": [105, 479, 540, 507], "type": "text", "content": "7. Provides an option for recovering the 3D displacement/strain/stress fields based on the linear beam theory (VABS 3.2.4)."}]}], "index": 7}, {"bbox": [88, 514, 367, 528], "type": "text", "lines": [{"bbox": [88, 514, 367, 528], "spans": [{"bbox": [88, 514, 367, 528], "type": "text", "content": "- As far as the quality of the code is concerned, VABS III"}]}], "index": 8}, {"bbox": [102, 535, 542, 705], "type": "text", "lines": [{"bbox": [102, 535, 542, 705], "spans": [{"bbox": [102, 535, 542, 705], "type": "text", "content": "1. Is restructured to change the error handling and error message handling, memory allocation and de-allocation, and I/O handling to facilitate its integration with other software environments (VABS 3.0).  \n2. Interprets and echoes all the input data for quicker identification of mistakes in the input file (VABS 3.0).  \n3. Is much faster than VABS 2.1.1 by modifying the mesh optimization algorithm and adopting a new approach to calculate the elemental finite element matrices (VABS 3.0).  \n4. Uses dynamic link libraries (DLLs) to encapsulate the analysis capability so that VABS has true plug-n-play capability which is convenient for integration into other environments. Now VABS can be used both as a standalone application and two callable libraries. The two callable libraries and the corresponding manual for developers can be requested separately (VABS 3.0)."}]}], "index": 9}], "discarded_blocks": [], "page_size": [612, 792], "page_idx": 4}, {"para_blocks": [{"bbox": [106, 104, 426, 118], "type": "text", "lines": [{"bbox": [106, 104, 426, 118], "spans": [{"bbox": [106, 104, 426, 118], "type": "text", "content": "5. Has more thorough and informative error handling (VABS 3.0)."}]}], "index": 0}, {"bbox": [70, 127, 540, 194], "type": "text", "lines": [{"bbox": [70, 127, 540, 194], "spans": [{"bbox": [70, 127, 540, 194], "type": "text", "content": "Quite a few bugs in VABS 2.1.1 have been corrected in VABS III and its later versions. One bug is associated with the modified linear solver. Because of this bug, for some very rare cases, VABS 2.1.1 provides some annoying couplings which are not supposed to be there. VABS 3.0 has no such anomaly. At least one bug related with the Trapeze effect inherited from the original VABS before 1998 has been corrected in VABS 3.0. A bug related with recovery is also corrected in VABS 3.2.3."}]}], "index": 1}, {"bbox": [70, 208, 538, 235], "type": "text", "lines": [{"bbox": [70, 208, 538, 235], "spans": [{"bbox": [70, 208, 538, 235], "type": "text", "content": "Starting from VABS 3.0, an evaluation version of VABS is free for anybody who asks. It allows the user to evaluate the code for one month before obtaining a permanent license."}]}], "index": 2}, {"bbox": [71, 250, 160, 263], "type": "title", "lines": [{"bbox": [71, 250, 160, 263], "spans": [{"bbox": [71, 250, 160, 263], "type": "text", "content": "3.11 VABS II"}]}], "index": 3, "level": 3, "line_avg_height": 18}, {"bbox": [70, 271, 540, 338], "type": "text", "lines": [{"bbox": [70, 271, 540, 338], "spans": [{"bbox": [70, 271, 540, 338], "type": "text", "content": "VABS II was released in June 2004, with the major enhancement to remove the need of asking the user to choose arbitrary point constraints and let the code determine the singularity and apply the corresponding constraints. Other improvements of VABS II include calculation of principal inertial axes, the mass matrix, and neutral axes, and a significant reduction of the computing time for large size problems."}]}], "index": 4}, {"bbox": [71, 357, 343, 373], "type": "title", "lines": [{"bbox": [71, 357, 343, 373], "spans": [{"bbox": [71, 357, 343, 373], "type": "text", "content": "4 VABS Features and Functionalities"}]}], "index": 5, "level": 2, "line_avg_height": 18}, {"bbox": [72, 384, 192, 398], "type": "title", "lines": [{"bbox": [72, 384, 192, 398], "spans": [{"bbox": [72, 384, 192, 398], "type": "text", "content": "4.1 VABS Features"}]}], "index": 6, "level": 3, "line_avg_height": 18}, {"bbox": [72, 406, 540, 431], "type": "text", "lines": [{"bbox": [72, 406, 540, 431], "spans": [{"bbox": [72, 406, 540, 431], "type": "text", "content": "Along with the features of previous versions, the most recent version of VABS has the following features:"}]}], "index": 7}, {"bbox": [81, 440, 542, 705], "type": "text", "lines": [{"bbox": [81, 440, 542, 705], "spans": [{"bbox": [81, 440, 542, 705], "type": "text", "content": "1. It is a highly modularized code written in the modern Fortran language. All the problem-dependent arrays are allocated dynamically during run time, and the user can use all the memory up to the limit of the machine. All the outstanding abilities of array handling in the modern Fortran language have been exploited.  \n2. It adopts highly efficient techniques to reduce the requirement of RAM and increase the computing efficiency. Now cross-sections as complex as real composite helicopter rotor blades with hundreds of layers can be easily handled on a laptop computer.  \n3. It has a general element library that includes all the typical 2D elements such as 3, 4, 5, 6-node triangular elements and 4, 5, 6, 7, 8, 9-node quadrilateral elements. Users are free to choose the type of elements, and different types of elements can be mixed within one mesh.  \n4. It can deal with arbitrary layups. Users can provide one parameter for the layup orientation and one parameter for the ply orientation to uniquely specify the material system in the global coordinate system. Nine parameters can be used for the ply orientation if a ply is highly curved and the ply angle is not uniform within an element.  \n5. It detects singularities and properly removes them to render the solution as a true representation of the theory. Older versions before VABS II dealt with them approximately by asking the users to input four constraints on three distinct, user-specified nodes. The arbitrariness of"}]}], "index": 8}], "discarded_blocks": [], "page_size": [612, 792], "page_idx": 5}, {"para_blocks": [{"bbox": [91, 105, 540, 132], "type": "text", "lines": [{"bbox": [91, 105, 540, 132], "spans": [{"bbox": [91, 105, 540, 132], "type": "text", "content": "the older approach can affect the refined models, and sometimes may even render the linear system unsolvable."}]}], "index": 0}, {"bbox": [82, 140, 541, 354], "type": "text", "lines": [{"bbox": [82, 140, 541, 354], "spans": [{"bbox": [82, 140, 541, 354], "type": "text", "content": "6. It applies the four constraints on the warping functions in such a way that the 3D elasticity solution can be reproduced for isotropic beams, correcting a mistake related with these constraints in previous versions.  \n7. It does not require the beam reference line to be the locus of cross-sectional area centroids. VABS can calculate the centroid for any arbitrary cross-section, and users can choose their own reference line for the convenience of the 1D global beam analysis.  \n8. It can deal with isotropic materials, orthotropic materials, and general anisotropic materials, while the previous versions treat all materials as orthotropic.  \n9. It can be quickly and conveniently integrated with other environments such as computer-aided design environments, multidisciplinary optimization environments, or commercial finite element packages.  \n10. VABS can be executed as a standalone executable in command line or called by other codes as a library."}]}], "index": 1}, {"bbox": [72, 368, 229, 381], "type": "title", "lines": [{"bbox": [72, 368, 229, 381], "spans": [{"bbox": [72, 368, 229, 381], "type": "text", "content": "4.2 VABS Functionalities"}]}], "index": 2, "level": 3, "line_avg_height": 18}, {"bbox": [72, 389, 541, 416], "type": "text", "lines": [{"bbox": [72, 389, 541, 416], "spans": [{"bbox": [72, 389, 541, 416], "type": "text", "content": "VABS is a general- purpose, cross- sectional analysis tool for computing inertial, stiffness, and strength properties of general cross- sections. Specifically, it has the following functionalities:"}]}], "index": 3}, {"bbox": [82, 424, 542, 693], "type": "text", "lines": [{"bbox": [82, 424, 542, 693], "spans": [{"bbox": [82, 424, 542, 693], "type": "text", "content": "1. <PERSON><PERSON><PERSON> the "}, {"bbox": [82, 424, 542, 693], "type": "inline_equation", "content": "6\\times 6"}, {"bbox": [82, 424, 542, 693], "type": "text", "content": " mass matrix, written in terms of the mass per unit length, and the first and second mass moments of inertia about the three axes. Based on the information provided by the mass matrix, VABS calculates the mass center, the principal inertial axes, the principal mass moments of inertia, and the mass-weighted radius of gyration.  \n2. Compute the geometrical center of the cross-section and the area of the cross-section.  \n3. Compute the "}, {"bbox": [82, 424, 542, 693], "type": "inline_equation", "content": "4\\times 4"}, {"bbox": [82, 424, 542, 693], "type": "text", "content": " stiffness matrix and compliance matrix for the classical model (also called the <PERSON><PERSON><PERSON><PERSON><PERSON> model) for prismatic or initially curved/twisted composite beams with normal or oblique cross-sections. Based on the classical model, VABS can calculate the location of tension center, the extension stiffness "}, {"bbox": [82, 424, 542, 693], "type": "inline_equation", "content": "(EA)"}, {"bbox": [82, 424, 542, 693], "type": "text", "content": ", the torsional stiffness "}, {"bbox": [82, 424, 542, 693], "type": "inline_equation", "content": "(GJ)"}, {"bbox": [82, 424, 542, 693], "type": "text", "content": ", the principal bending stiffnesses "}, {"bbox": [82, 424, 542, 693], "type": "inline_equation", "content": "(EI_{22}"}, {"bbox": [82, 424, 542, 693], "type": "text", "content": " and "}, {"bbox": [82, 424, 542, 693], "type": "inline_equation", "content": "EI_{33}"}, {"bbox": [82, 424, 542, 693], "type": "text", "content": "), and the principal bending axes.  \n4. Compute the "}, {"bbox": [82, 424, 542, 693], "type": "inline_equation", "content": "6\\times 6"}, {"bbox": [82, 424, 542, 693], "type": "text", "content": " stiffness matrix and compliance matrix for the <PERSON><PERSON><PERSON> model for prismatic or initially curved/twisted composite beams with normal cross-sections. Based on the <PERSON><PERSON><PERSON> model, VABS can calculate the location of the shear center, the principal shear stiffnesses "}, {"bbox": [82, 424, 542, 693], "type": "inline_equation", "content": "(GA_{22}"}, {"bbox": [82, 424, 542, 693], "type": "text", "content": " and "}, {"bbox": [82, 424, 542, 693], "type": "inline_equation", "content": "GA_{33}"}, {"bbox": [82, 424, 542, 693], "type": "text", "content": "), and the principal shear axes.  \n5. Compute the "}, {"bbox": [82, 424, 542, 693], "type": "inline_equation", "content": "5\\times 5"}, {"bbox": [82, 424, 542, 693], "type": "text", "content": " stiffness matrix and compliance matrix for the <PERSON><PERSON><PERSON> model for prismatic or initially curved/twisted composite beams with normal cross-sections, which is important for thin-walled beams with open sections."}]}], "index": 4}], "discarded_blocks": [], "page_size": [612, 792], "page_idx": 6}, {"para_blocks": [{"type": "image", "bbox": [69, 105, 506, 234], "blocks": [{"bbox": [69, 105, 506, 234], "lines": [{"bbox": [69, 105, 506, 234], "spans": [{"bbox": [69, 105, 506, 234], "type": "image", "image_path": "519e6ae4a74e569d337606d4b1c97426f32527cac863f2a3d3be96f229633a85.jpg"}]}], "index": 0, "type": "image_body"}, {"bbox": [205, 248, 405, 262], "lines": [{"bbox": [205, 248, 405, 262], "spans": [{"bbox": [205, 248, 405, 262], "type": "text", "content": "Figure 1: VABS beam coordinate system"}]}], "index": 1, "type": "image_caption"}], "index": 0}, {"bbox": [82, 281, 540, 308], "type": "text", "lines": [{"bbox": [82, 281, 540, 308], "spans": [{"bbox": [82, 281, 540, 308], "type": "text", "content": "6. Compute the trapeze effects, a nonlinear effect important for beams under large centrifugal forces. The composite beam could be either prismatic or initially twisted and curved."}]}], "index": 2}, {"bbox": [83, 317, 540, 398], "type": "text", "lines": [{"bbox": [83, 317, 540, 398], "spans": [{"bbox": [83, 317, 540, 398], "type": "text", "content": "7. Compute 3D pointwise displacement, strain, and stress fields using the global behavior of a 1D global beam analysis using the classical model, the <PERSON><PERSON><PERSON> model, or the <PERSON><PERSON><PERSON> model. Multiple recovery runs can be performed for different inputs of global beam responses without repeating the homogenization analysis. The recovered stress/strain fields are evaluated both at the nodal positions and Gauss points. They are expressed in both the material coordinate system and the beam coordinate system."}]}], "index": 3}, {"bbox": [83, 407, 540, 448], "type": "text", "lines": [{"bbox": [83, 407, 540, 448], "spans": [{"bbox": [83, 407, 540, 448], "type": "text", "content": "8. Compute sectional damping matrix for composite beams. The computation is based on the simple concept of scaling stiffness related matrices with the lamina damping coefficient specified for each material."}]}], "index": 4}, {"bbox": [83, 456, 540, 498], "type": "text", "lines": [{"bbox": [83, 456, 540, 498], "spans": [{"bbox": [83, 456, 540, 498], "type": "text", "content": "9. Compute hygrothermal effects of composite beams due to temperature and moisture changes. As a companion capability, VABS Conduction is developed to carry out a dimensional reduction for the 3D conduction problem, which can be requested separately."}]}], "index": 5}, {"bbox": [78, 506, 539, 533], "type": "text", "lines": [{"bbox": [78, 506, 539, 533], "spans": [{"bbox": [78, 506, 539, 533], "type": "text", "content": "10. Compute the failure index and strength ratio distribution over the cross-section, and the strength ration for the entire cross-section."}]}], "index": 6}, {"bbox": [70, 552, 231, 568], "type": "title", "lines": [{"bbox": [70, 552, 231, 568], "spans": [{"bbox": [70, 552, 231, 568], "type": "text", "content": "5 VABS Conventions"}]}], "index": 7, "level": 2, "line_avg_height": 22}, {"bbox": [70, 579, 540, 605], "type": "text", "lines": [{"bbox": [70, 579, 540, 605], "spans": [{"bbox": [70, 579, 540, 605], "type": "text", "content": "To understand the inputs and interpret outputs of the program correctly, we need to explain some conventions used by VABS."}]}], "index": 8}, {"bbox": [70, 620, 540, 701], "type": "text", "lines": [{"bbox": [70, 620, 540, 701], "spans": [{"bbox": [70, 620, 540, 701], "type": "text", "content": "First, VABS uses a right hand system, the beam coordinate system, denoted as "}, {"bbox": [70, 620, 540, 701], "type": "inline_equation", "content": "x_{1},x_{2}"}, {"bbox": [70, 620, 540, 701], "type": "text", "content": " and "}, {"bbox": [70, 620, 540, 701], "type": "inline_equation", "content": "x_{3}"}, {"bbox": [70, 620, 540, 701], "type": "text", "content": " with "}, {"bbox": [70, 620, 540, 701], "type": "inline_equation", "content": "x_{1}"}, {"bbox": [70, 620, 540, 701], "type": "text", "content": " as the beam axis and "}, {"bbox": [70, 620, 540, 701], "type": "inline_equation", "content": "x_{2}"}, {"bbox": [70, 620, 540, 701], "type": "text", "content": " and "}, {"bbox": [70, 620, 540, 701], "type": "inline_equation", "content": "x_{3}"}, {"bbox": [70, 620, 540, 701], "type": "text", "content": " as the local Cartesian coordinates of the cross- section (see Figure 1). Usually, for rotor blades, "}, {"bbox": [70, 620, 540, 701], "type": "inline_equation", "content": "x_{1}"}, {"bbox": [70, 620, 540, 701], "type": "text", "content": " is along the direction of the span and points to the tip, "}, {"bbox": [70, 620, 540, 701], "type": "inline_equation", "content": "x_{2}"}, {"bbox": [70, 620, 540, 701], "type": "text", "content": " is along the direction of the trailing edge to the leading- edge of the airfoil and points to the direction of the leading edge, and "}, {"bbox": [70, 620, 540, 701], "type": "inline_equation", "content": "x_{3}"}, {"bbox": [70, 620, 540, 701], "type": "text", "content": " points upward so that "}, {"bbox": [70, 620, 540, 701], "type": "inline_equation", "content": "x_{1},x_{2},x_{3}"}, {"bbox": [70, 620, 540, 701], "type": "text", "content": " form a right- hand system. Often the origin of "}, {"bbox": [70, 620, 540, 701], "type": "inline_equation", "content": "x_{1}"}, {"bbox": [70, 620, 540, 701], "type": "text", "content": " is located at the root of the blade, yet the user is free to choose the origin of "}, {"bbox": [70, 620, 540, 701], "type": "inline_equation", "content": "x_{2}"}, {"bbox": [70, 620, 540, 701], "type": "text", "content": " and "}, {"bbox": [70, 620, 540, 701], "type": "inline_equation", "content": "x_{3}"}]}], "index": 9}], "discarded_blocks": [], "page_size": [612, 792], "page_idx": 7}, {"para_blocks": [{"bbox": [69, 104, 541, 213], "type": "text", "lines": [{"bbox": [69, 104, 541, 213], "spans": [{"bbox": [69, 104, 541, 213], "type": "text", "content": "at an arbitrary point of the cross- section, or particular references with physical meaning such as the mass center, geometry center, tension center, or shear center. Detailed information is needed to define the cross- sectional geometric domain spanned by "}, {"bbox": [69, 104, 541, 213], "type": "inline_equation", "content": "x_{2}"}, {"bbox": [69, 104, 541, 213], "type": "text", "content": " and "}, {"bbox": [69, 104, 541, 213], "type": "inline_equation", "content": "x_{3}"}, {"bbox": [69, 104, 541, 213], "type": "text", "content": " and the materials that occupy that domain. Also, certain characteristics along the span direction, such as initial curvature/twist, are needed for cross- sectional analyses when they are not equal to zero. The obliqueness should be specified when reference cross- section is not normal to the reference line, such as the case of a swept wing. It is noted that the beam coordinate system is the same as the undeformed beam coordinate system "}, {"bbox": [69, 104, 541, 213], "type": "inline_equation", "content": "b"}, {"bbox": [69, 104, 541, 213], "type": "text", "content": " defined in Ref. [7]."}]}], "index": 0}, {"type": "image", "bbox": [69, 232, 507, 361], "blocks": [{"bbox": [69, 232, 507, 361], "lines": [{"bbox": [69, 232, 507, 361], "spans": [{"bbox": [69, 232, 507, 361], "type": "image", "image_path": "51eff09c6076802547cd79ec92a3b9d5c2889a6f8b7b1a889b2bd09ecac1f806.jpg"}]}], "index": 1, "type": "image_body"}, {"bbox": [83, 373, 525, 387], "lines": [{"bbox": [83, 373, 525, 387], "spans": [{"bbox": [83, 373, 525, 387], "type": "text", "content": "Figure 2: VABS triangular element node numbering and corresponding integration schemes"}]}], "index": 2, "type": "image_caption"}], "index": 1}, {"bbox": [69, 401, 541, 455], "type": "text", "lines": [{"bbox": [69, 401, 541, 455], "spans": [{"bbox": [69, 401, 541, 455], "type": "text", "content": "Second, VABS numbers the nodes of each element in the counterclockwise direction, as shown in Figure 2 for triangular elements and Figure 3 for quadrilateral elements. Nodes 1, 2, and 3 of the triangular elements and nodes 1, 2, 3, and 4 of the quadrilateral elements are at the corners. Nodes 5, 6, 7 of the triangular elements and nodes 5, 6, 7, 8, 9 for quadrilateral elements are optional."}]}], "index": 3}, {"bbox": [69, 469, 540, 495], "type": "text", "lines": [{"bbox": [69, 469, 540, 495], "spans": [{"bbox": [69, 469, 540, 495], "type": "text", "content": "The recovered 3D displacements are values at each node expressed in the VABS beam coordinate system (Figure 1). However, stresses and strains are most accurately evaluated at Gauss"}]}], "index": 4}, {"type": "image", "bbox": [69, 512, 506, 670], "blocks": [{"bbox": [69, 512, 506, 670], "lines": [{"bbox": [69, 512, 506, 670], "spans": [{"bbox": [69, 512, 506, 670], "type": "image", "image_path": "8538e4f5502f3a238a278f8b6306fb5b905d9c95e31bd220071ee85872a54c8c.jpg"}]}], "index": 5, "type": "image_body"}, {"bbox": [77, 681, 531, 695], "lines": [{"bbox": [77, 681, 531, 695], "spans": [{"bbox": [77, 681, 531, 695], "type": "text", "content": "Figure 3: VABS quadrilateral element node numbering and corresponding integration schemes"}]}], "index": 6, "type": "image_caption"}], "index": 5}], "discarded_blocks": [], "page_size": [612, 792], "page_idx": 8}, {"para_blocks": [{"bbox": [69, 104, 541, 186], "type": "text", "lines": [{"bbox": [69, 104, 541, 186], "spans": [{"bbox": [69, 104, 541, 186], "type": "text", "content": "integration points. Gauss integration schemes for different orders of the two types of elements are also shown in Figures 2 and 3. The red interior points correspond to the integration scheme for linear elements and the green interior points correspond to the integration scheme for quadratic elements. VABS can also recover 3D stresses and strains at each node as suggested by our industry users. The recovered stresses and strains are expressed in both the beam coordinate system and the material coordinate system which is needed for failure analysis of composite materials."}]}], "index": 0}, {"type": "image", "bbox": [123, 205, 487, 328], "blocks": [{"bbox": [123, 205, 487, 328], "lines": [{"bbox": [123, 205, 487, 328], "spans": [{"bbox": [123, 205, 487, 328], "type": "image", "image_path": "59b12f42468b9f4c1c6a5072d53dafbe0ecbb095d5557cf23047efd55330f783.jpg"}]}], "index": 1, "type": "image_body"}, {"bbox": [223, 341, 387, 354], "lines": [{"bbox": [223, 341, 387, 354], "spans": [{"bbox": [223, 341, 387, 354], "type": "text", "content": "Figure 4: VABS layup convention"}]}], "index": 2, "type": "image_caption"}], "index": 1}, {"bbox": [70, 368, 540, 436], "type": "text", "lines": [{"bbox": [70, 368, 540, 436], "spans": [{"bbox": [70, 368, 540, 436], "type": "text", "content": "VABS allows the user to choose any unit system of convenience. However, it is necessary to be consistent in the choice of units to avoid confusion. Particularly, users must never use the pound as a unit of mass to avoid confusion. When pounds are used for force and feet for length, the unit of mass must be "}, {"bbox": [70, 368, 540, 436], "type": "inline_equation", "content": "\\mathrm{slug} = \\mathrm{lb - sec^2 / ft}"}, {"bbox": [70, 368, 540, 436], "type": "text", "content": ". If inches are used for length along with pounds for force, then the unit of mass must be "}, {"bbox": [70, 368, 540, 436], "type": "inline_equation", "content": "\\mathrm{lb - sec^2 / in}"}, {"bbox": [70, 368, 540, 436], "type": "text", "content": "."}]}], "index": 3}, {"bbox": [69, 449, 541, 654], "type": "text", "lines": [{"bbox": [69, 449, 541, 654], "spans": [{"bbox": [69, 449, 541, 654], "type": "text", "content": "Finally, to understand the VABS input convention for composite layups, we need to find relationships among three coordinate systems: the beam coordinate system "}, {"bbox": [69, 449, 541, 654], "type": "inline_equation", "content": "(x_{1},x_{2},x_{3})"}, {"bbox": [69, 449, 541, 654], "type": "text", "content": " used by the user to define the geometry, the material system "}, {"bbox": [69, 449, 541, 654], "type": "inline_equation", "content": "(e_1,e_2,e_3)"}, {"bbox": [69, 449, 541, 654], "type": "text", "content": " used by the user to define the material properties, and an intermediate one to define the ply plane "}, {"bbox": [69, 449, 541, 654], "type": "inline_equation", "content": "(y_{1},y_{2},y_{3})"}, {"bbox": [69, 449, 541, 654], "type": "text", "content": ". As shown in Figure 4, the ply coordinate system "}, {"bbox": [69, 449, 541, 654], "type": "inline_equation", "content": "(y_{1},y_{2},y_{3})"}, {"bbox": [69, 449, 541, 654], "type": "text", "content": " is formed by rotating the global coordinate system "}, {"bbox": [69, 449, 541, 654], "type": "inline_equation", "content": "(x_{1},x_{2},x_{3})"}, {"bbox": [69, 449, 541, 654], "type": "text", "content": " in the right- hand sense about "}, {"bbox": [69, 449, 541, 654], "type": "inline_equation", "content": "x_{1}"}, {"bbox": [69, 449, 541, 654], "type": "text", "content": " by the amount "}, {"bbox": [69, 449, 541, 654], "type": "inline_equation", "content": "0\\leq \\theta_{1}\\leq 360^{\\circ}"}, {"bbox": [69, 449, 541, 654], "type": "text", "content": ". Then, the ply coordinate system "}, {"bbox": [69, 449, 541, 654], "type": "inline_equation", "content": "(y_{1},y_{2},y_{3})"}, {"bbox": [69, 449, 541, 654], "type": "text", "content": " is rotated about "}, {"bbox": [69, 449, 541, 654], "type": "inline_equation", "content": "y_{3}"}, {"bbox": [69, 449, 541, 654], "type": "text", "content": " in the right- hand sense by the amount "}, {"bbox": [69, 449, 541, 654], "type": "inline_equation", "content": "- 90^{\\circ}\\leq \\theta_{3}\\leq 90^{\\circ}"}, {"bbox": [69, 449, 541, 654], "type": "text", "content": " to form the material system "}, {"bbox": [69, 449, 541, 654], "type": "inline_equation", "content": "(e_1,e_2,e_3)"}, {"bbox": [69, 449, 541, 654], "type": "text", "content": ", the range of "}, {"bbox": [69, 449, 541, 654], "type": "inline_equation", "content": "\\theta_{3}"}, {"bbox": [69, 449, 541, 654], "type": "text", "content": " being the same as commonly defined in the field of composite materials. Here, we use the box- beam section depicted in Figure 5 to illustrate VABS layup conventions. Here, "}, {"bbox": [69, 449, 541, 654], "type": "inline_equation", "content": "x_{1}"}, {"bbox": [69, 449, 541, 654], "type": "text", "content": " is pointing toward the reader, "}, {"bbox": [69, 449, 541, 654], "type": "inline_equation", "content": "x_{2}"}, {"bbox": [69, 449, 541, 654], "type": "text", "content": " is pointing to the right side of the reader, and "}, {"bbox": [69, 449, 541, 654], "type": "inline_equation", "content": "x_{3}"}, {"bbox": [69, 449, 541, 654], "type": "text", "content": " is pointing upward vertically. For the upper wall: "}, {"bbox": [69, 449, 541, 654], "type": "inline_equation", "content": "\\theta_{1} = 0^{\\circ}"}, {"bbox": [69, 449, 541, 654], "type": "text", "content": "; the left wall: "}, {"bbox": [69, 449, 541, 654], "type": "inline_equation", "content": "\\theta_{1} = 90^{\\circ}"}, {"bbox": [69, 449, 541, 654], "type": "text", "content": "; the lower wall: "}, {"bbox": [69, 449, 541, 654], "type": "inline_equation", "content": "\\theta_{1} = 180^{\\circ}"}, {"bbox": [69, 449, 541, 654], "type": "text", "content": "; the right wall: "}, {"bbox": [69, 449, 541, 654], "type": "inline_equation", "content": "\\theta_{1} = 270^{\\circ}"}, {"bbox": [69, 449, 541, 654], "type": "text", "content": ". For all the walls "}, {"bbox": [69, 449, 541, 654], "type": "inline_equation", "content": "\\theta_{3} = \\theta"}, {"bbox": [69, 449, 541, 654], "type": "text", "content": " for the box- beam in Figure 5 because all the fibers are rotating positively about "}, {"bbox": [69, 449, 541, 654], "type": "inline_equation", "content": "y_{3} / e_{3}"}, {"bbox": [69, 449, 541, 654], "type": "text", "content": " by the angle "}, {"bbox": [69, 449, 541, 654], "type": "inline_equation", "content": "\\theta"}, {"bbox": [69, 449, 541, 654], "type": "text", "content": ". The users can specify their own stacking sequences. The stacking sequences expressed from the innermost layer to the outermost layer for each wall are often used."}]}], "index": 4}], "discarded_blocks": [], "page_size": [612, 792], "page_idx": 9}], "_backend": "vlm", "_version_name": "2.1.10"}