"""PyVabs - A Python library for [add your description here].

This package provides [describe main functionality].
"""

__version__ = "0.1.0"
__author__ = "Su Tian"
__email__ = "<EMAIL>"

# Import main classes/functions here
from .core import PyVabsCore, create_instance
from .utils import setup_logging, load_config, save_config
from .vabs_interface import (
    VABSHomogenizationResult,
    VABSRecoveryResult,
    constitutive_modeling,
    recovery,
    create_material_matrix,
    create_simple_mesh,
    parse_vabs_output_stress_strain,
)


def hello() -> str:
    """Return a greeting message from pyvabs.

    Returns:
        str: A greeting message.
    """
    return "Hello from pyvabs!"


__all__ = [
    "hello",
    "PyVabsCore",
    "create_instance",
    "setup_logging",
    "load_config",
    "save_config",
    # VABS interface
    "VABSHomogenizationResult",
    "VABSRecoveryResult",
    "constitutive_modeling",
    "recovery",
    "create_material_matrix",
    "create_simple_mesh",
    "parse_vabs_output_stress_strain",
]
