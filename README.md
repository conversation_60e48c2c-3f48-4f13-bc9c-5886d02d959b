# PyVabs

[![CI](https://github.com/yourusername/pyvabs/workflows/CI/badge.svg)](https://github.com/yourusername/pyvabs/actions)
[![codecov](https://codecov.io/gh/yourusername/pyvabs/branch/main/graph/badge.svg)](https://codecov.io/gh/yourusername/pyvabs)
[![PyPI version](https://badge.fury.io/py/pyvabs.svg)](https://badge.fury.io/py/pyvabs)
[![Python 3.13+](https://img.shields.io/badge/python-3.13+-blue.svg)](https://www.python.org/downloads/)

A Python library for VABS (Variational Asymptotic Beam Sectional) analysis - a Python rewrite of the Fortran VABS code for beam cross-sectional analysis.

## Features

- 🚀 **Fast and Efficient**: Optimized for performance
- 🔧 **Easy to Use**: Simple and intuitive API
- 📝 **Well Documented**: Comprehensive documentation and examples
- 🧪 **Well Tested**: High test coverage with pytest
- 🔍 **Type Safe**: Full type hints support
- 🛠️ **Developer Friendly**: Great development experience with modern tooling

## Quick Start

### Installation

```bash
pip install pyvabs
```

Or with uv:

```bash
uv add pyvabs
```

### Basic Usage

```python
import pyvabs
from pyvabs.core import PyVabsCore

# Basic greeting
print(pyvabs.hello())  # "Hello from pyvabs!"

# Create a core instance
core = PyVabsCore("my_instance", {"debug": True})

# Process some data
data = ["item1", "item2", "item3"]
result = core.process(data)
print(result)  # ['processed_item1', 'processed_item2', 'processed_item3']

# Get instance information
info = core.get_info()
print(info)
```

## Documentation

Full documentation is available at [https://pyvabs.readthedocs.io](https://pyvabs.readthedocs.io)

## Development

### Prerequisites

- Python 3.13 or higher
- [uv](https://docs.astral.sh/uv/) package manager

### Setup Development Environment

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/pyvabs.git
   cd pyvabs
   ```

2. Set up the development environment:
   ```bash
   python scripts/setup_dev.py
   ```

   Or manually:
   ```bash
   uv sync --all-extras --dev
   uv run pre-commit install
   ```

### Running Tests

```bash
# Run all tests
python scripts/run_tests.py

# Run tests with coverage
python scripts/run_tests.py --coverage

# Run specific test file
python scripts/run_tests.py tests/test_core.py

# Run linting and type checking
python scripts/run_tests.py --all
```

### Code Quality

This project uses several tools to maintain code quality:

- **Black**: Code formatting
- **isort**: Import sorting
- **flake8**: Linting
- **mypy**: Type checking
- **pytest**: Testing
- **pre-commit**: Git hooks

Run all quality checks:

```bash
uv run black src tests
uv run isort src tests
uv run flake8 src tests
uv run mypy src
uv run pytest
```

### Building Documentation

```bash
cd docs
uv run sphinx-build -b html . _build/html
```

## Project Structure

```
pyvabs/
├── src/pyvabs/          # Main package source code
│   ├── __init__.py      # Package initialization
│   ├── core.py          # Core functionality
│   ├── utils.py         # Utility functions
│   └── py.typed         # Type information marker
├── tests/               # Test suite
│   ├── test_core.py     # Core functionality tests
│   ├── test_utils.py    # Utility function tests
│   └── test_init.py     # Package initialization tests
├── docs/                # Documentation source
├── examples/            # Usage examples
├── scripts/             # Development scripts
├── .github/workflows/   # GitHub Actions CI/CD
├── pyproject.toml       # Project configuration
├── README.md           # This file
└── LICENSE             # MIT License
```

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request. For major changes, please open an issue first to discuss what you would like to change.

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Make your changes
4. Run the test suite (`python scripts/run_tests.py --all`)
5. Commit your changes (`git commit -m 'Add some amazing feature'`)
6. Push to the branch (`git push origin feature/amazing-feature`)
7. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Changelog

See [CHANGELOG.md](CHANGELOG.md) for a list of changes and version history.

## Support

- 📖 [Documentation](https://pyvabs.readthedocs.io)
- 🐛 [Issue Tracker](https://github.com/yourusername/pyvabs/issues)
- 💬 [Discussions](https://github.com/yourusername/pyvabs/discussions)