# VABS Manual for Users*

August 1, 2024

## 1 Introduction

VABS (Variational Asymptotic Beam Sectional Analysis) is a code implementing the beam theo- . ries  \(1 - 13,13 - 24\)  based on the concept of simplifying the original nonlinear three- dimensional (3D) analysis of slender structures into a two- dimensional (2D) cross- sectional analysis and a one- dimensional (1D) nonlinear beam analysis using the variational asymptotic method25

VABS takes a finite element mesh of the cross- section including all the details of geometry and material as inputs to perform a homogenization analysis to compute sectional properties including inertial properties and structural properties. These properties are needed for the 1D beam analysis to predict the global behavior of slender structures. VABS can also perform a dehomogenization analysis to compute the distributions of 3D displacements/strains/stresses, and failure indexes and strength ratios over the cross- section based on the global behavior of the 1D beam analysis.

Since most of the theoretical details are presented in pertinent papers and collected in the book by <PERSON><PERSON>, this manual will only serve to help readers get started using VABS to solve their own composite beam problems. This manual addresses the history of the code, its features, functionalities, conventions, inputs, outputs, maintenance, and tech support.

## 2 VABS History

The research project that gave birth to VABS was initiated by Prof. <PERSON> when he was first introduced to the variational asymptotic method by Prof<PERSON> at Georgia Tech in 1989 and has been ongoing ever since till the time of writing. The program name VABS first appeared in [1].

The original version of VABS was based on a research code written by Prof. <PERSON>snik. The fall semester of 1998, when Prof. <PERSON> began his graduate study at Georgia Tech, marked the beginning

of the transition of VABS from a research code to a production design and analysis tool for practicing engineers. The code was rewritten from scratch using the modern Fortran language, with all unnecessary restrictions eliminated, and the computing and memory efficiency greatly enhanced. At the same time, Prof. Ce<PERSON><PERSON> was continuing his work on VABS for piezoelectric materials at MIT and later at University of Michigan. Profs. Hodges and Yu continue their work on VABS for multiphysics modeling at Georgia Tech, Utah State University, and Purdue University. For this reason, there are two variants of VABS: the Georgia Tech/Utah State/Purdue VABS, released and maintained by Prof. Yu, and UM/VABS, released and maintained by Prof. Cesnik. From henceforth in this manual the term VABS will refer only to the Georgia Tech/Utah State/Purdue VABS, and what follows is only applicable to this code.

Many researchers and engineers all over the world are actively using VABS. VABS is becoming a standard tool for design and analysis of composite slender structures such as helicopter rotor blades, wind turbine blades, high aspect ratio wings, UAM/eVTOL/UAV blades, propellers, landing gear, composite tubes, etc.

## 3 What is New in Different VABS versions

### 3.1 What is New in VABS 4.1

The new capabilities of VABS 4.1 are:

1. Perform dehomogenization for multiple load cases in terms of forces and moments corresponding to the Euler-Bernoulli model and the Timoshenko model.  
2. Output the modes and corresponding ratios for failure criteria with identifiable modes such as failure criterion 1, 2, 5.  
3. Provide documentation for the user-define failure criterion capability.  
4. Enable users to suppress the output of other stress/strain files except the average stresses/strains for each element in the ele file.  
5. Remove damping input for no damping analysis.  
6. Output the classical stiffness matrix and compliance matrix at the shear center.  
7. Enable comment lines in the inputs for better readability.  
8. Output the time and date the code is compiled and released.  
9. Provide simple instructions when VABS, VABS -h, or VABS -help is issued in the command line.  
10. Simplify the installation process without administrator privilege.

### 3.2 What is New in VABS 4.0

The new capabilities of VABS 4.0 are:

1. Compute pointwise distributions of failure indexes and strength ratios over the cross-section under a given load. 
2. Compute the safety margin (the minimum strength ratio among all elements) of the crosssection under a given load. 
3. Output the nodal stress/strain values according to the original numbering of the finite element nodes. 
4. Output the complete set of engineering properties commonly used in conventional beam analysis including extension stiffness  \((EA)\)  , torsional stiffness  \((GJ)\)  , principal bending stiffnesses  \((EI_{22},EI_{33})\)  , principal shear stiffness  \((GA_{22},GA_{33})\)  , tension center, shear center, principal inertial axes, principal bending axes, and principal shear axes. 
5. Since VABS 4.0, we change the executable name to VABS.

### 3.3 What is New in VABS 3.9

The main new feature of VABS 3.9 is prediction of sectional damping matrix based on the lamina damping coefficients. VABS 3.9 can also output the cross- sectional area. Another feature is that VABS 3.9 is now distributed as a single library.

### 3.4 What is New in VABS 3.8

The main new feature of VABS 3.8 is a new license manager to allow more versatile license mechanisms including node locked licenses and floating licenses. The user does not have to put the license in the same folder as the input file as it was the case for previous versions. Instead, the user can use a node locked license file stored in the same folder as the VABS executable or obtain a floating license from a license server.

Since VABS 3.8, we provide free academic licenses for students and professors to use the full version of VABS for teaching and academic research.

Since VABS 3.8, we provide both Linux and Windows versions.

### 3.5 What is New in VABS 3.7

The main new feature of VABS 3.7 is to carry out the recovery up to the second order which provides a better prediction for the 3D recovered fields in comparison to known exact solutions.

### 3.6 What is New in VABS 3.6

3.6 What is New in VABS 3.6The main new feature of VABS 3.6 is to use an improved method for optimizing the numbering of finite element mesh. For large problems, it is much faster than the method used in previous versions. The most recent version of fortran compiler is used for compiling the code resulting in much faster recovery.

### 3.7 What is New in VABS 3.5

3.7 What is New in VABS 3.5The main new feature of VABS 3.5 is for oblique cross- sectional analysis, the inputs are given in the oblique cross- sectional system while in previous versions, the inputs are given in the normal cross- sectional coordinates. Also starting VABS 3.5, users can use long names of input file, including spaces in the path and file names.

### 3.8 What is New in VABS 3.4

3.8 What is New in VABS 3.4The main new feature is expanding  \(\sqrt{g}\)  in the modeling for initially curved/twisted beams. For some cases such a change made significant differences for obtaining first and second correction of the stiffness matrix due to initial curvature and twist. Such a change is verified using an initially curved strip for which an elasticity solution is obtainable. The input file for this case is isorrectTrif2. sg. A 64- bit version of VABS is also available since VABS 3.4.

### 3.9 What is New in VABS 3.3

3.9 What is New in VABS 3.3The main new feature is introducing a new input format, and keeping the previous input format as optional. In the new format, the user only needs to provide one real number for  \(\theta_{1}\)  as few users take advantage of the nine real numbers for  \(\theta_{1}\) , which is useful for elements with highly curved edges. In the new format, we introduce layer definition so that a layer type instead of material type is provided for each element. Each layer is defined through a unique combination of material type and layerup angle  \(\theta_{3}\) . It is more economical than assigning  \(\theta_{3}\)  for each element, as what we have done in the previous format, because the number of layers usually is much less than the number of elements. These two changes reduce approximately  \(3 / 4\)  of real numbers needed for VABS inputs, saving space and time. These changes will also simplify the development of VABS preprocessors as it is easier to compute just one number for  \(\theta_{1}\)  for each element.

### 3.10 VABS III and What is New

VABS was originally designed to run as a standalone code and its error handling, memory allocation/deallocation, and I/O were handled with this use in mind. However, in recent years, more and more users began to explore the possibility of using VABS in a design environment. This motivates the major upgrade of VABS to VABS III through restructuring the code.

Since the first release of VABS III, a few users have asked the difference between VABS III and previous versions, in particular VABS 2.1.1 which was the last release and the code accompanying Prof. Hodges' book? Overall, VABS III is a much improved code in both accuracy and efficiency. The main difference can be described according to the following two aspects.

- As far as functionalities concerned, VABS III

1. Uses the correct constraints so that it can reproduce the 3D elasticity theory for isotropic prismatic beams. This change affects the warping functions, and affects all stiffness models except the classical one. Such a correction enables VABS to reproduce the 3D elasticity theory for isotropic prismatic beams and thus enables VABS to provide a better modeling for prismatic or initially curved/twisted composite beams (VABS 3.0).

2. Recovers 3D stress/strain fields at each node in addition to Gauss points. The recovered 3D stress/strain fields are expressed in both the beam coordinate system and the material coordinate system. VABS 2.1.1 only recovers 3D stress/strain fields at Gauss points expressed in the beam coordinate system. For visualization, nodal values are convenient. To apply failure criteria of composite materials, stresses/strains expressed in the material coordinate system are needed (VABS 3.0).

3. Handles isotropic, orthotropic, and anisotropic material differently. Previous versions treat all materials as orthotropic only and must take a total of 9 elastic constants. VABS III allows general anisotropic material with as many as 21 elastic constants and isotropic materials with as few as 2 elastic constants (VABS 3.0).

4. Can model hygrothermal effects of composite beams due to temperature and moisture changes. As a companion capability, VABS Conduction is developed to carry out a dimensional reduction for the 3D conduction problem. VABS Conduction can be requested separately (VABS 3.1).

5. Updates the transformation procedure into the Timoshenko model from the asymptotic energy. A new perturbation method is developed to capture the effects due to initial curvatures/twist during the transformation. The prediction for Timoshenko stiffness is generally improved, even for some prismatic beams (VABS 3.2).

6. Outputs the average of 3D stresses/strains within each element for convenience of postprocessing (VABS 3.2.2).

7. Provides an option for recovering the 3D displacement/strain/stress fields based on the linear beam theory (VABS 3.2.4).

- As far as the quality of the code is concerned, VABS III

1. Is restructured to change the error handling and error message handling, memory allocation and de-allocation, and I/O handling to facilitate its integration with other software environments (VABS 3.0).  
2. Interprets and echoes all the input data for quicker identification of mistakes in the input file (VABS 3.0).  
3. Is much faster than VABS 2.1.1 by modifying the mesh optimization algorithm and adopting a new approach to calculate the elemental finite element matrices (VABS 3.0).  
4. Uses dynamic link libraries (DLLs) to encapsulate the analysis capability so that VABS has true plug-n-play capability which is convenient for integration into other environments. Now VABS can be used both as a standalone application and two callable libraries. The two callable libraries and the corresponding manual for developers can be requested separately (VABS 3.0).

5. Has more thorough and informative error handling (VABS 3.0).

Quite a few bugs in VABS 2.1.1 have been corrected in VABS III and its later versions. One bug is associated with the modified linear solver. Because of this bug, for some very rare cases, VABS 2.1.1 provides some annoying couplings which are not supposed to be there. VABS 3.0 has no such anomaly. At least one bug related with the Trapeze effect inherited from the original VABS before 1998 has been corrected in VABS 3.0. A bug related with recovery is also corrected in VABS 3.2.3.

Starting from VABS 3.0, an evaluation version of VABS is free for anybody who asks. It allows the user to evaluate the code for one month before obtaining a permanent license.

### 3.11 VABS II

VABS II was released in June 2004, with the major enhancement to remove the need of asking the user to choose arbitrary point constraints and let the code determine the singularity and apply the corresponding constraints. Other improvements of VABS II include calculation of principal inertial axes, the mass matrix, and neutral axes, and a significant reduction of the computing time for large size problems.

## 4 VABS Features and Functionalities

### 4.1 VABS Features

Along with the features of previous versions, the most recent version of VABS has the following features:

1. It is a highly modularized code written in the modern Fortran language. All the problem-dependent arrays are allocated dynamically during run time, and the user can use all the memory up to the limit of the machine. All the outstanding abilities of array handling in the modern Fortran language have been exploited.  
2. It adopts highly efficient techniques to reduce the requirement of RAM and increase the computing efficiency. Now cross-sections as complex as real composite helicopter rotor blades with hundreds of layers can be easily handled on a laptop computer.  
3. It has a general element library that includes all the typical 2D elements such as 3, 4, 5, 6-node triangular elements and 4, 5, 6, 7, 8, 9-node quadrilateral elements. Users are free to choose the type of elements, and different types of elements can be mixed within one mesh.  
4. It can deal with arbitrary layups. Users can provide one parameter for the layup orientation and one parameter for the ply orientation to uniquely specify the material system in the global coordinate system. Nine parameters can be used for the ply orientation if a ply is highly curved and the ply angle is not uniform within an element.  
5. It detects singularities and properly removes them to render the solution as a true representation of the theory. Older versions before VABS II dealt with them approximately by asking the users to input four constraints on three distinct, user-specified nodes. The arbitrariness of

the older approach can affect the refined models, and sometimes may even render the linear system unsolvable.

6. It applies the four constraints on the warping functions in such a way that the 3D elasticity solution can be reproduced for isotropic beams, correcting a mistake related with these constraints in previous versions.  
7. It does not require the beam reference line to be the locus of cross-sectional area centroids. VABS can calculate the centroid for any arbitrary cross-section, and users can choose their own reference line for the convenience of the 1D global beam analysis.  
8. It can deal with isotropic materials, orthotropic materials, and general anisotropic materials, while the previous versions treat all materials as orthotropic.  
9. It can be quickly and conveniently integrated with other environments such as computer-aided design environments, multidisciplinary optimization environments, or commercial finite element packages.  
10. VABS can be executed as a standalone executable in command line or called by other codes as a library.

### 4.2 VABS Functionalities

VABS is a general- purpose, cross- sectional analysis tool for computing inertial, stiffness, and strength properties of general cross- sections. Specifically, it has the following functionalities:

1. Compute the  \(6\times 6\)  mass matrix, written in terms of the mass per unit length, and the first and second mass moments of inertia about the three axes. Based on the information provided by the mass matrix, VABS calculates the mass center, the principal inertial axes, the principal mass moments of inertia, and the mass-weighted radius of gyration.  
2. Compute the geometrical center of the cross-section and the area of the cross-section.  
3. Compute the  \(4\times 4\)  stiffness matrix and compliance matrix for the classical model (also called the Euler-Bernoulli model) for prismatic or initially curved/twisted composite beams with normal or oblique cross-sections. Based on the classical model, VABS can calculate the location of tension center, the extension stiffness  \((EA)\) , the torsional stiffness  \((GJ)\) , the principal bending stiffnesses  \((EI_{22}\)  and  \(EI_{33}\) ), and the principal bending axes.  
4. Compute the  \(6\times 6\)  stiffness matrix and compliance matrix for the Timoshenko model for prismatic or initially curved/twisted composite beams with normal cross-sections. Based on the Timoshenko model, VABS can calculate the location of the shear center, the principal shear stiffnesses  \((GA_{22}\)  and  \(GA_{33}\) ), and the principal shear axes.  
5. Compute the  \(5\times 5\)  stiffness matrix and compliance matrix for the Vlasov model for prismatic or initially curved/twisted composite beams with normal cross-sections, which is important for thin-walled beams with open sections.

![](images/519e6ae4a74e569d337606d4b1c97426f32527cac863f2a3d3be96f229633a85.jpg)  
Figure 1: VABS beam coordinate system

6. Compute the trapeze effects, a nonlinear effect important for beams under large centrifugal forces. The composite beam could be either prismatic or initially twisted and curved.

7. Compute 3D pointwise displacement, strain, and stress fields using the global behavior of a 1D global beam analysis using the classical model, the Timoshenko model, or the Vlasov model. Multiple recovery runs can be performed for different inputs of global beam responses without repeating the homogenization analysis. The recovered stress/strain fields are evaluated both at the nodal positions and Gauss points. They are expressed in both the material coordinate system and the beam coordinate system.

8. Compute sectional damping matrix for composite beams. The computation is based on the simple concept of scaling stiffness related matrices with the lamina damping coefficient specified for each material.

9. Compute hygrothermal effects of composite beams due to temperature and moisture changes. As a companion capability, VABS Conduction is developed to carry out a dimensional reduction for the 3D conduction problem, which can be requested separately.

10. Compute the failure index and strength ratio distribution over the cross-section, and the strength ration for the entire cross-section.

## 5 VABS Conventions

To understand the inputs and interpret outputs of the program correctly, we need to explain some conventions used by VABS.

First, VABS uses a right hand system, the beam coordinate system, denoted as  \(x_{1},x_{2}\)  and  \(x_{3}\)  with  \(x_{1}\)  as the beam axis and  \(x_{2}\)  and  \(x_{3}\)  as the local Cartesian coordinates of the cross- section (see Figure 1). Usually, for rotor blades,  \(x_{1}\)  is along the direction of the span and points to the tip,  \(x_{2}\)  is along the direction of the trailing edge to the leading- edge of the airfoil and points to the direction of the leading edge, and  \(x_{3}\)  points upward so that  \(x_{1},x_{2},x_{3}\)  form a right- hand system. Often the origin of  \(x_{1}\)  is located at the root of the blade, yet the user is free to choose the origin of  \(x_{2}\)  and  \(x_{3}\)

at an arbitrary point of the cross- section, or particular references with physical meaning such as the mass center, geometry center, tension center, or shear center. Detailed information is needed to define the cross- sectional geometric domain spanned by  \(x_{2}\)  and  \(x_{3}\)  and the materials that occupy that domain. Also, certain characteristics along the span direction, such as initial curvature/twist, are needed for cross- sectional analyses when they are not equal to zero. The obliqueness should be specified when reference cross- section is not normal to the reference line, such as the case of a swept wing. It is noted that the beam coordinate system is the same as the undeformed beam coordinate system  \(b\)  defined in Ref. [7].

![](images/51eff09c6076802547cd79ec92a3b9d5c2889a6f8b7b1a889b2bd09ecac1f806.jpg)  
Figure 2: VABS triangular element node numbering and corresponding integration schemes

Second, VABS numbers the nodes of each element in the counterclockwise direction, as shown in Figure 2 for triangular elements and Figure 3 for quadrilateral elements. Nodes 1, 2, and 3 of the triangular elements and nodes 1, 2, 3, and 4 of the quadrilateral elements are at the corners. Nodes 5, 6, 7 of the triangular elements and nodes 5, 6, 7, 8, 9 for quadrilateral elements are optional.

The recovered 3D displacements are values at each node expressed in the VABS beam coordinate system (Figure 1). However, stresses and strains are most accurately evaluated at Gauss

![](images/8538e4f5502f3a238a278f8b6306fb5b905d9c95e31bd220071ee85872a54c8c.jpg)  
Figure 3: VABS quadrilateral element node numbering and corresponding integration schemes

integration points. Gauss integration schemes for different orders of the two types of elements are also shown in Figures 2 and 3. The red interior points correspond to the integration scheme for linear elements and the green interior points correspond to the integration scheme for quadratic elements. VABS can also recover 3D stresses and strains at each node as suggested by our industry users. The recovered stresses and strains are expressed in both the beam coordinate system and the material coordinate system which is needed for failure analysis of composite materials.

![](images/59b12f42468b9f4c1c6a5072d53dafbe0ecbb095d5557cf23047efd55330f783.jpg)  
Figure 4: VABS layup convention

VABS allows the user to choose any unit system of convenience. However, it is necessary to be consistent in the choice of units to avoid confusion. Particularly, users must never use the pound as a unit of mass to avoid confusion. When pounds are used for force and feet for length, the unit of mass must be  \(\mathrm{slug} = \mathrm{lb - sec^2 / ft}\) . If inches are used for length along with pounds for force, then the unit of mass must be  \(\mathrm{lb - sec^2 / in}\) .

Finally, to understand the VABS input convention for composite layups, we need to find relationships among three coordinate systems: the beam coordinate system  \((x_{1},x_{2},x_{3})\)  used by the user to define the geometry, the material system  \((e_1,e_2,e_3)\)  used by the user to define the material properties, and an intermediate one to define the ply plane  \((y_{1},y_{2},y_{3})\) . As shown in Figure 4, the ply coordinate system  \((y_{1},y_{2},y_{3})\)  is formed by rotating the global coordinate system  \((x_{1},x_{2},x_{3})\)  in the right- hand sense about  \(x_{1}\)  by the amount  \(0\leq \theta_{1}\leq 360^{\circ}\) . Then, the ply coordinate system  \((y_{1},y_{2},y_{3})\)  is rotated about  \(y_{3}\)  in the right- hand sense by the amount  \(- 90^{\circ}\leq \theta_{3}\leq 90^{\circ}\)  to form the material system  \((e_1,e_2,e_3)\) , the range of  \(\theta_{3}\)  being the same as commonly defined in the field of composite materials. Here, we use the box- beam section depicted in Figure 5 to illustrate VABS layup conventions. Here,  \(x_{1}\)  is pointing toward the reader,  \(x_{2}\)  is pointing to the right side of the reader, and  \(x_{3}\)  is pointing upward vertically. For the upper wall:  \(\theta_{1} = 0^{\circ}\) ; the left wall:  \(\theta_{1} = 90^{\circ}\) ; the lower wall:  \(\theta_{1} = 180^{\circ}\) ; the right wall:  \(\theta_{1} = 270^{\circ}\) . For all the walls  \(\theta_{3} = \theta\)  for the box- beam in Figure 5 because all the fibers are rotating positively about  \(y_{3} / e_{3}\)  by the angle  \(\theta\) . The users can specify their own stacking sequences. The stacking sequences expressed from the innermost layer to the outermost layer for each wall are often used.