# VABS Interface Documentation

This document describes the Python interface to VABS (Variational Asymptotic Beam Sectional) analysis functions implemented in PyVabs.

## Overview

The VABS interface provides Python bindings for the two main VABS analysis functions:

1. **Homogenization Analysis** (`constitutive_modeling`): Computes effective beam properties from cross-sectional analysis
2. **Recovery Analysis** (`recovery`): Recovers 3D stress/strain fields from 1D beam analysis results

## Main Functions

### constitutive_modeling()

Performs VABS homogenization analysis to compute beam constitutive properties.

**Purpose**: Analyze a beam cross-section to determine effective beam properties including:
- Mass matrix and inertial properties
- Stiffness matrices for different beam theories (classical, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>)
- Geometric properties (centers of mass, tension, shear)
- Thermal properties

**Key Parameters**:
- `inp_name`: Input file name
- `nnode`, `nelem`, `nmate`, `nlayer`: Problem dimensions
- `coord`: Nodal coordinates (nnode, 2)
- `element`: Element connectivity (nelem, 9)
- `material`: Material properties (nmate, 21)
- `layup_angle`: Layer orientations
- Analysis flags: `<PERSON><PERSON><PERSON>_I`, `curved_I`, `oblique_I`, etc.

**Returns**: `VABSHomogenizationResult` containing all computed beam properties

### recovery()

Performs VABS recovery analysis to compute 3D stress/strain fields.

**Purpose**: Given 1D beam analysis results, recover the full 3D displacement, stress, and strain fields throughout the cross-section.

**Key Parameters**:
- All homogenization parameters (mesh, materials, etc.)
- `recover_I`: Recovery type (1=nonlinear, 2=linear, 3=failure analysis)
- `disp_1D`: Global beam displacements (3,)
- `force_1D`: 1D stress resultants (6,)
- `dir_cos_1D`: Direction cosine matrix (3,3)
- Load derivatives for higher-order analysis

**Returns**: `VABSRecoveryResult` containing 3D fields and failure analysis

## Helper Functions

### create_material_matrix()

Creates material property arrays for different material types:

```python
# Isotropic material
material = create_material_matrix('isotropic', E1=200e9, nu12=0.3)

# Orthotropic material (e.g., carbon fiber)
material = create_material_matrix(
    'orthotropic',
    E1=150e9, E2=10e9, E3=10e9,
    G12=5e9, G13=5e9, G23=3e9,
    nu12=0.3, nu13=0.3, nu23=0.4
)
```

### create_simple_mesh()

Generates simple rectangular meshes for basic cross-sections:

```python
# Create 20mm x 2mm cross-section with 4x1 elements
coords, elements = create_simple_mesh(
    width=0.020, height=0.002, nx=4, ny=1
)
```

### parse_vabs_output_stress_strain()

Parses VABS stress/strain output data:

```python
# Parse stress/strain data in beam coordinates
results = parse_vabs_output_stress_strain(ss_data, 'beam')
positions = results['position']  # (n, 2) x2, x3 coordinates
strains = results['strain']      # (n, 6) strain components
stresses = results['stress']     # (n, 6) stress components
```

## Data Structures

### VABSHomogenizationResult

Container for homogenization analysis results:

```python
class VABSHomogenizationResult:
    # Mass properties
    mass: FloatArray2D          # (6,6) mass matrix
    area: float                 # cross-sectional area
    xm2, xm3: float            # mass center coordinates
    I22, I33: float            # mass moments of inertia
    
    # Stiffness properties
    ST: FloatArray2D           # (6,6) Timoshenko stiffness
    ST_F: FloatArray2D         # (6,6) Timoshenko flexibility
    Aee_Aee_F: FloatArray2D    # (4,4) classical beam stiffness
    
    # Geometric properties
    Xg2, Xg3: float           # geometry center
    Xe2, Xe3: float           # tension center
    Sc1, Sc2: float           # shear center
    
    # Error information
    error: str
```

### VABSRecoveryResult

Container for recovery analysis results:

```python
class VABSRecoveryResult:
    # 3D field recovery
    disp_3D_F: FloatArray2D    # (nnode,5) 3D displacements
    ss_F: FloatArray2D         # (k_F,26) stress/strain at Gauss points
    ss_nd_F: FloatArray2D      # (nd_F,26) stress/strain at nodes
    ss_elem: FloatArray2D      # (nelem,24) element-averaged values
    
    # Failure analysis
    fi_pos: FloatArray2D       # (nelem,2) failure index and strength ratio
    mode_pos: List[str]        # (nelem,) failure modes
    str_comp: FloatArray2D     # (nelem,6) component strength ratios
    
    # Error information
    error: str
```

## VABS Constants

The interface defines important VABS constants:

```python
DBL = 8                # Double precision indicator
TOLERANCE = 1e-12      # Numerical tolerance
NDIM = 2              # Cross-section dimensions
MAX_NODE_ELEM = 9     # Maximum nodes per element
NE_ID = 4             # Classical beam DOF
```

## Input Validation

The interface provides validation functions:

- `validate_homogenization_inputs()`: Validates inputs for homogenization
- `validate_recovery_inputs()`: Validates inputs for recovery analysis

These check array dimensions, parameter ranges, and consistency.

## Usage Example

```python
import numpy as np
from pyvabs.vabs_interface import (
    constitutive_modeling, recovery,
    create_material_matrix, create_simple_mesh
)

# Create material
carbon_fiber = create_material_matrix(
    'orthotropic', E1=150e9, E2=10e9, E3=10e9,
    G12=5e9, G13=5e9, G23=3e9, nu12=0.3, nu13=0.3, nu23=0.4
)

# Create mesh
coords, elements = create_simple_mesh(0.020, 0.002, 4, 1)
nnode, nelem = coords.shape[0], elements.shape[0]

# Setup analysis
material = carbon_fiber.reshape(1, -1)
layup_angles = np.array([0.0, 90.0, 0.0])  # [0/90/0] layup

# Homogenization
homo_result = constitutive_modeling(
    inp_name="beam.dat", format_I=1,
    mat_type_layer=np.array([1,1,1]), layup_angle=layup_angles,
    LAY_CONST=1, nlayer=3, Timoshenko_I=1, curved_I=0,
    # ... other parameters
    coord=coords, element=elements, material=material,
    # ... remaining parameters
)

# Recovery (after 1D beam analysis)
recovery_result = recovery(
    # ... homogenization parameters
    recover_I=2,  # Linear recovery
    disp_1D=np.array([0.001, 0.0, 0.0]),
    force_1D=np.array([0.0, 0.0, 1000.0, 0.0, 0.0, 0.0]),
    # ... other recovery parameters
)
```

## Implementation Status

**Current Status**: Function declarations and interfaces are complete, but the actual VABS library calls are not yet implemented. The functions currently return placeholder results with "Not implemented yet" error messages.

**Next Steps**:
1. Implement actual VABS library integration (DLL calls or Python bindings)
2. Add comprehensive error handling for VABS-specific errors
3. Extend with additional VABS features (thermal analysis, failure analysis, etc.)
4. Add performance optimizations and memory management

## Testing

Comprehensive test suite covers:
- Function interfaces and parameter validation
- Data structure initialization and manipulation
- Helper function correctness
- Input validation edge cases
- Error handling

Run tests with: `uv run pytest tests/test_vabs_interface.py -v`

## References

- VABS Developer Manual (docs/refs/vabs-dev-doc/)
- Original VABS Fortran interface documentation
- PyVabs examples (examples/vabs_example.py)
