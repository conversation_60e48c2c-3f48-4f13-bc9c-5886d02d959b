[{"type": "text", "text": "5. If analysis is equal to 20, VABS will carry out a dehomogenization analysis to recover 3D displacements, strains, stresses based on the linear beam theory with large outputs.", "page_idx": 0}, {"type": "text", "text": "6. If analysis is equal to 3, VABS will carry out a dehomogenization analysis to evaluate the distribution of failure index and strength ratio over the cross-section, and the strength ratio of the entire cross-section.", "page_idx": 0}, {"type": "text", "text": "If analysis is equal to 1, 2, 3, we can also provide another command line argument nload as the total number of loads for VABS to perform the corresponding dehomogenization analysis. If this argument does not exist, it will perform the dehomogenization analysis for a single load case.", "page_idx": 0}, {"type": "text", "text": "7 VABS Inputs", "text_level": 1, "page_idx": 0}, {"type": "text", "text": "Although a few preprocessors, such as PreVABS, have been developed to create VABS input files, it is still beneficial for advanced users, particularly those who want to embedded VABS in their own design environment, to understand the meaning of the input data.", "page_idx": 0}, {"type": "text", "text": "Starting from VABS 4.0, the inputs for the VABS are separated into two files: homogenization input file and dehomogenization input file. VABS homogenization run only requires the homogenization input file with a name of the user's choice. The dehomogenization input file associated with the homogenization input file with extension glb. In other words, if the homogenization input file name is input_file_name, the dehomogenization input file must be input_file_name glb. Empty lines or comment lines can be used in the input file for readability. The comment line must start with \"!\"", "page_idx": 0}, {"type": "text", "text": "7.1 Homogenization Input File", "text_level": 2, "page_idx": 0}, {"type": "text", "text": "The first line lists two newly introduced integer flags arranged as: \"format_flag nlayer\". If the first flag is 1, the input is prepared in the new format, otherwise, it is prepared in the old format. The second integer provides the number of layers in the section. Note, here layer is defined as a unique combination of material type and layup orientation, it does not necessarily corresponds to the definition used in the manufacturing sense. For example, even if a section is made of a single isotropic material, we consider it has one layer. Hence, nlayer should be always given a value greater than 1 if format_flag=1 and it is not used when using the old format.", "page_idx": 0}, {"type": "text", "text": "The second line has two flags arranged as: \"Timoshenko_flag damping_flag thermal_flag\". The first flag, Timoshenko_flag, can be only 1 or 0. If it is 1, VABS will construct both the classical model (also called the <PERSON><PERSON><PERSON><PERSON> <PERSON> model) and the <PERSON><PERSON><PERSON> model. If it is 0, it will only construct the classical model. The second flag, damping_flag, can be equal to 0 or 1. If it is equal to 0, VABS will not compute the damping matrix for the section. If it is equal to 1, VABS will compute the damping matrix. The third flag, thermal_flag, can be equal to 0 or 3. If it is equal to zero, VABS will carry out a pure mechanical analysis. If it is equal to 3, VABS will carry out a one- way coupled thermoelastic analysis.", "page_idx": 0}, {"type": "image", "img_path": "images/f412d7871b06261b1b1be61a4cd2b9a1bae678964ff5eeab0895486a331347f7.jpg", "image_caption": [], "image_footnote": [], "page_idx": 1}, {"type": "text", "text": "The third line has four flags arranged as: “curve_flag oblique_flag trapeze_flag <PERSON><PERSON><PERSON>_flag.” These flags can be only 1 or 0. Their uses are explained in the following:", "page_idx": 1}, {"type": "text", "text": "1. To model initially curved and twisted beams, curve flag is 1, and three real numbers for the twist (  \\(k_{1}\\)  ) and curvatures (  \\(k_{2}\\)  and  \\(k_{3}\\)  ) should be provided in the very next line.", "page_idx": 1}, {"type": "text", "text": "2. To model oblique cross-sections, oblique flag is 1, and two real numbers are needed in the following line to specify the orientation of an oblique reference cross-section, see Figure 6 for a sketch of such a cross-section. The first number is cosine of the angle between normal of the oblique section (  \\(y_{1}\\)  ) and beam axis  \\(x_{1}\\)  . The second number is cosine of the angle between  \\(y_{2}\\)  of the oblique section and beam axis  \\(\\left(x_{1}\\right)\\)  . The summation of the square of these two numbers should not be greater than 1.0 in double precision. The inputs including coordinates, material properties, etc. and the outputs including mass matrix, stiffness matrix, etc. are given in the oblique system, the  \\(y_{i}\\)  coordinate system as shown in Figure 6. Note that this feature is only enabled for the classical beam model.", "page_idx": 1}, {"type": "text", "text": "3. To obtain the trapeze effect, trapeze flag is 1.", "page_idx": 1}, {"type": "text", "text": "4. To obtain the <PERSON><PERSON><PERSON> model, Vlasov flag is 1. Vlasov flag can be 1 only if <PERSON><PERSON><PERSON> flag is 1. VABS will first construct the <PERSON><PERSON><PERSON> model, which determines the location of the shear center. If the shear center is not at the origin of the beam coordinate system, VABS will move the origin of beam coordinate system to the shear center and repeat the calculation to obtain the <PERSON><PERSON><PERSON> model.", "page_idx": 1}, {"type": "text", "text": "The next line lists three integers arranged as: \"nnode nelem nmate,\" where nnode is the total number of nodes, nelem the total number of elements, and nmate the total number of material types.", "page_idx": 2}, {"type": "text", "text": "The next nnode lines are the coordinates for each node arranged as: \"node_no  \\(x_{2}\\) \\(x_{3}\\)  \" where node_no is an integer representing the unique number assigned to each node and  \\(x_{2}\\) \\(x_{3}\\)  are two real numbers describing the location  \\((x_{2},x_{3})\\)  of the node. Although the arrangement of node_no is not necessary to be consecutive, every node starting from 1 to nnode should be present.", "page_idx": 2}, {"type": "text", "text": "The next nelem lines list 10 integers for the nodes for each element (also known as the connectivity relations). They are arranged as: \"elem_no node_1 node_2 node_3 node_4 node_5 node_6 node_7 node_8 node_9,\" where elem_no is the number of element and node_i  \\((i = 1,2,\\ldots ,9)\\)  are nodes of this element. If a node is not present in the element, the value is 0. If node_4 is 0, the element is a triangular element; see Figures 2 and 3 for the VABS numbering convention. Although the arrangement of elem_no is not necessary to be consecutive, every element starting from 1 to nelem should be present.", "page_idx": 2}, {"type": "text", "text": "If format  \\(\\cdot f l a g = 1\\)  , that is, if the new format is used, the next nelem lines list the layer type and the layer plane angle  \\(\\left(\\theta_{1}\\right)\\)  for each element as: elem_no layer_type  \\(\\theta_{1}\\)  , where layer_type is an integer representing which layer the element elem_no belongs to, and  \\(\\theta_{1}\\)  is a real number describing the layer plane angle for the element. Here,  \\(\\theta_{1}\\)  is assumed to be constant for each element, thus it can be calculated at any material point belonging to the element, such as the centroid, or computed as the average of  \\(\\theta_{1}\\)  of all the points within the element. Although the arrangement of elem_no is not necessary to be consecutive, every element starting from 1 to nelem should be present. For isotropic materials,  \\(\\theta_{1}\\)  will not enter the calculations.", "page_idx": 2}, {"type": "text", "text": "If format  \\(\\cdot f l a g = 1\\)  , that is, if the old format is used, the next nelem lines list the material type and layup parameters for each element as: elem_no material_type  \\(\\theta_{3}\\) \\(\\theta_{1}(9)\\)  , where material_type is an integer representing the type of the material for the element elem_no,  \\(\\theta_{3}\\)  is a real number representing the layup angle in degrees for this element, and  \\(\\theta_{1}(9)\\)  is an array storing nine real numbers for the layer plane angles at the nodes of this element. For simplification, if the ply orientation can be considered as uniform for this element,  \\(\\theta_{1}(1)\\)  stores the layer plane angle and  \\(\\theta_{1}(2) = 540^{\\circ}\\)  , and all the rest can be zeros or other real numbers because they do not enter the calculation. If the element has fewer than nine nodes, zeros are to be input for the corresponding missing nodes, as in the case for connectivity. Although the arrangement of elem_no is not necessary to be consecutive, every element starting from 1 to nelem should be present. For isotropic materials, neither  \\(\\theta_{3}\\)  nor  \\(\\theta_{1}(9)\\)  will enter the calculations.", "page_idx": 2}, {"type": "text", "text": "If format  \\(\\cdot f l a g = 1\\)  , that is, if the new format is used, the next nlayer lines define the layers used in the section. They are arranged as: layer_id mate_type  \\(\\theta_{3}\\)  , where layer_id is an integer denoting the identification number for each layer, mate_type is an integer denoting the material type used in the layer, and  \\(\\theta_{3}\\)  is a real number denoting the layup orientation. For example, if layer 1 is made of material 1 and having  \\(- 15^{\\circ}\\)  layup, we will provide the information as 1 1  15.0. If damping  \\(\\cdot f l a g\\)  is 1, a damping coefficient for each layer is also needed to input right after  \\(\\theta_{3}\\)  . In others words, the input should be arranged as layer id mate_type  \\(\\theta_{3}\\)  damping_layer, with damping_layer indicating", "page_idx": 2}, {"type": "text", "text": "the damping coefficient for the layer.", "page_idx": 3}, {"type": "text", "text": "The next nmate blocks defines the material properties. They are arranged as:", "page_idx": 3}, {"type": "text", "text": "mat id orth", "page_idx": 3}, {"type": "text", "text": "const1 const2 ....", "page_idx": 3}, {"type": "text", "text": "where mat id is the number of material type, orth is the flag to indicate whether the material is isotropic (0), orthotropic (1) or general anisotropic (2). The rest are material constants.", "page_idx": 3}, {"type": "text", "text": "For isotropic materials, orth is 0, if thermal_flag is 0, there are 3 constants arranged as:", "page_idx": 3}, {"type": "text", "text": "\\(E\\) \\(\\nu\\)", "page_idx": 3}, {"type": "text", "text": "\\(\\rho\\)", "page_idx": 3}, {"type": "text", "text": "where  \\(E\\)  is the Young's modulus,  \\(\\nu\\)  is the <PERSON><PERSON><PERSON>'s ratio, and  \\(\\rho\\)  is the density of the material. Poisson's ratio must be greater than - 1.0 and less than 0.5 for isotropic materials, although VABS allows users to input values that are very close to those limits.", "page_idx": 3}, {"type": "text", "text": "If thermal_flag is 3 and orth is 0, and there are 4 constants arranged as:", "page_idx": 3}, {"type": "text", "text": "\\(E\\) \\(\\nu\\)", "page_idx": 3}, {"type": "text", "text": "\\(\\rho\\)", "page_idx": 3}, {"type": "text", "text": "\\(\\alpha\\)", "page_idx": 3}, {"type": "text", "text": "where  \\(\\alpha\\)  is the coefficient of thermal expansion (CTE).", "page_idx": 3}, {"type": "text", "text": "For orthotropic materials, orth is 1, if thermal_flag is 0, there are 10 constants arranged as:", "page_idx": 3}, {"type": "text", "text": "\\(E_{1}\\) \\(E_{2}\\) \\(E_{3}\\)", "page_idx": 3}, {"type": "text", "text": "\\(G_{12}\\) \\(G_{13}\\) \\(G_{23}\\)", "page_idx": 3}, {"type": "text", "text": "\\(\\nu_{12}\\) \\(\\nu_{13}\\) \\(\\nu_{23}\\)", "page_idx": 3}, {"type": "text", "text": "\\(\\rho\\)", "page_idx": 3}, {"type": "text", "text": "including the Young's moduli  \\((E_{1},E_{2}\\)  , and  \\(E_{3}\\)  ), the shear moduli  \\((G_{12},G_{13}\\)  , and  \\(G_{23}\\)  ), the Poisson's ratios  \\((\\nu_{12},\\nu_{13}\\)  , and  \\(\\nu_{23}\\)  ), and the mass density  \\((\\rho)\\)  . The convention of values is such that these values will be used to form the following the <PERSON><PERSON>'s law for composite materials:", "page_idx": 3}, {"type": "equation", "text": "\n\\[\n\\left\\{\\begin{array}{l}{{\\epsilon_{11}}}\\\\ {{2\\epsilon_{12}}}\\\\ {{2\\epsilon_{13}}}\\\\ {{\\epsilon_{22}}}\\\\ {{2\\epsilon_{23}}}\\\\ {{\\epsilon_{33}}}\\end{array}\\right\\}=\\left[\\begin{array}{c c c c c c c}{{\\frac{1}{E_{1}}}}&{{0}}&{{0}}&{{-\\frac{\\nu_{12}}{E_{1}}}}&{{0}}&{{-\\frac{\\nu_{13}}{E_{1}}}}\\\\ {{0}}&{{\\frac{1}{G_{12}}}}&{{0}}&{{0}}&{{0}}&{{0}}\\\\ {{0}}&{{0}}&{{\\frac{1}{G_{13}}}}&{{0}}&{{0}}&{{0}}\\\\ {{-\\frac{\\nu_{12}}{E_{1}}}}&{{0}}&{{0}}&{{\\frac{1}{E_{2}}}}&{{0}}&{{-\\frac{\\nu_{23}}{E_{2}}}}\\\\ {{0}}&{{0}}&{{0}}&{{0}}&{{\\frac{1}{G_{23}}}}&{{0}}\\\\ {{-\\frac{\\nu_{13}}{E_{1}}}}&{{0}}&{{0}}&{{-\\frac{\\nu_{23}}{E_{2}}}}&{{0}}&{{\\frac{1}{E_{3}}}}\\end{array}\\right]\\left\\{\\begin{array}{l}{{\\sigma_{11}}}\\\\ {{\\sigma_{12}}}\\\\ {{\\sigma_{13}}}\\\\ {{\\sigma_{22}}}\\\\ {{\\sigma_{23}}}\\\\ {{\\sigma_{33}}}\\end{array}\\right\\}\n\\]\n", "text_format": "latex", "page_idx": 3}, {"type": "text", "text": "If thermal_flag is 3 and orth is 1, and there are 13 constants arranged as:", "page_idx": 3}, {"type": "text", "text": "\\(E_{1}\\) \\(E_{2}\\) \\(E_{3}\\)", "page_idx": 3}, {"type": "text", "text": "\\(G_{12}\\) \\(G_{13}\\) \\(G_{23}\\)", "page_idx": 3}, {"type": "text", "text": "\\(\\nu_{12}\\) \\(\\nu_{13}\\) \\(\\nu_{23}\\)", "page_idx": 3}, {"type": "text", "text": "\\(\\rho\\)", "page_idx": 3}, {"type": "text", "text": "\\(\\alpha_{11}\\) \\(\\alpha_{22}\\) \\(\\alpha_{33}\\)", "page_idx": 3}, {"type": "text", "text": "where  \\(\\alpha_{11},\\alpha_{22},\\alpha_{33}\\)  are the CTEs along three directions.", "page_idx": 3}, {"type": "text", "text": "For general anisotropic materials, orth is 2, if thermal_flag is 0, there are 22 constants arranged as:", "page_idx": 4}, {"type": "equation", "text": "\n\\[\n\\begin{array}{c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c} & c_{11} & c_{12} & c_{13} & c_{14} & c_{15} & c_{16} & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & \\\\ & c_{22} & c_{23} & c_{24} & c_{25} & c_{26} & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & \\\\ & & c_{33} & c_{34} & c_{35} & c_{36} & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & \\end{array}\n\\]\n", "text_format": "latex", "page_idx": 4}, {"type": "text", "text": "These values are defined using the following <PERSON><PERSON>'s law:", "page_idx": 4}, {"type": "equation", "text": "\n\\[\n\\left\\{ \\begin{array}{l}{\\sigma_{11}}\\\\ {\\sigma_{12}}\\\\ {\\sigma_{13}}\\\\ {\\sigma_{22}}\\\\ {\\sigma_{23}}\\\\ {\\sigma_{33}} \\end{array} \\right\\} = \\left[ \\begin{array}{l l l l l l l}{c_{11}} & {c_{12}} & {c_{13}} & {c_{14}} & {c_{15}} & {c_{16}}\\\\ {c_{12}} & {c_{22}} & {c_{23}} & {c_{24}} & {c_{25}} & {c_{26}}\\\\ {c_{13}} & {c_{23}} & {c_{33}} & {c_{34}} & {c_{35}} & {c_{36}}\\\\ {c_{14}} & {c_{24}} & {c_{34}} & {c_{44}} & {c_{45}} & {c_{46}}\\\\ {c_{15}} & {c_{25}} & {c_{35}} & {c_{45}} & {c_{55}} & {c_{56}}\\\\ {c_{16}} & {c_{26}} & {c_{36}} & {c_{46}} & {c_{56}} & {c_{66}} \\end{array} \\right]\\left\\{ \\begin{array}{l}{\\epsilon_{11}}\\\\ {2\\epsilon_{12}}\\\\ {2\\epsilon_{13}}\\\\ {\\epsilon_{22}}\\\\ {2\\epsilon_{23}}\\\\ {\\epsilon_{33}} \\end{array} \\right\\}\n\\]\n", "text_format": "latex", "page_idx": 4}, {"type": "text", "text": "If thermal_flag is 3 and orth is 2, there are 28 constants arranged as:", "page_idx": 4}, {"type": "equation", "text": "\n\\[\n\\begin{array}{r l}{c_{11}} & {c_{12} c_{13} c_{14} c_{15} c_{16}}\\\\ {c_{22} c_{23} c_{24} c_{25} c_{26}}\\\\ {c_{33} c_{34} c_{35} c_{36}}\\\\ {c_{44} c_{45} c_{46}}\\\\ {c_{55} c_{56}}\\\\ {c_{66}}\\\\ {\\rho} \\end{array}\n\\]\n", "text_format": "latex", "page_idx": 4}, {"type": "text", "text": "\\(\\alpha_{11} 2\\alpha_{12} 2\\alpha_{13} \\alpha_{22} 2\\alpha_{23} \\alpha_{33}\\)  where  \\(\\alpha_{ij}\\) , with  \\(i = 1,2,3\\)  and  \\(j = 1,2,3\\) , are the components of the second- order CTE tensor. CTEs corresponding to the shear strains are multiplied by two because the engineering shear strains are twice of the corresponding tensorial shear strains. The material constants are expressed in the material coordinate system (see Figure 4). If the material properties are given in a different coordinate system, or the arrangement of stresses and strains are different from what VABS uses, a proper transformation of the material properties is needed.", "page_idx": 4}, {"type": "text", "text": "If damping_flag is 1, a damping coefficient is input on the very next line following the density input. For example, if orth=0 and thermal_flag=3 (thermoelastic analysis with isotropic materials), the material constants are arranged as:", "page_idx": 4}, {"type": "equation", "text": "\n\\[\n\\begin{array}{c}{{E}}\\\\ {{\\rho}}\\\\ {{\\gamma}}\\\\ {{\\alpha}}\\end{array}\n\\]\n", "text_format": "latex", "page_idx": 4}, {"type": "text", "text": "where  \\(\\gamma\\)  is a scalar representing the material damping property. It is noted that the damping coefficients for each layer and for each material are additive. In other words, the total damping coefficient used to scale the stiffness- related matrices is damping_layer +  \\(\\gamma\\) .", "page_idx": 4}, {"type": "text", "text": "If thermal_flag is equal to 3, we also need to provide the following nnode lines for temperature for each node arranged as: \"node_no  \\(T\\)  \" where node_no is an integer representing the unique number assigned to each node and  \\(T\\)  is a real number describing the temperature of the node. These temperature values can be calculated either from a 3D heat conduction analysis or using VABS Conduction, which is a generalization of the VABS approach for heat conduction analysis. Although the arrangement of node_no is not necessary to be consecutive, every node starting from 1 to nnode should be present.", "page_idx": 5}, {"type": "text", "text": "Now, we have prepared all the inputs necessary for performing the homogenization run to compute the inertial properties and structural properties of the cross- section. That is, when analysis in Section 6 does not exist.", "page_idx": 5}, {"type": "text", "text": "7.2 Dehomogenization Input File", "text_level": 2, "page_idx": 5}, {"type": "text", "text": "If analysis in Section 6 is equal to 1,2, 3, users should provide additional information in the dehomogenization input file for VABS to perform a dehomogenization analysis. A corresponding homogenization analysis must be run before carrying out the dehomogenization analysis.", "page_idx": 5}, {"type": "text", "text": "If analysis is equal to 3, VABS will perform failure analysis of the cross- section. Strength properties for each material must be provided in the dehomogenization input file. Strength properties for each material include a failure criterion and corresponding strength constants. Two lines will be inserted and the inputs needed for failure analyses should be arranged as:", "page_idx": 5}, {"type": "text", "text": "failure_criterion num_of_constants const1 const2 const3", "page_idx": 5}, {"type": "text", "text": "failure_criterion is an integer identifier for the failure criterion. num_of_constants indicates the number of strength constants needed for the corresponding failure criterion. const1 const2 const3 are the corresponding strength constants. It is noted that this block of data should be corresponding to the material block in the homogenization input file. In other words, for each material with mat_id, we need to provide such information.", "page_idx": 5}, {"type": "text", "text": "failure_criterion can be equal to 1, 2, 3, 4, 5, and another number greater than 10. For isotropic material, 1 is max principal stress criterion, 2 is max principal strain criterion, 3 is max shear stress criterion (also commonly called Tresca criterion), 4 max shear strain criterion, and 5 is <PERSON><PERSON> criterion. For anisotropic materials, 1 is max stress criterion for anisotropic materials, 2 is max strain criterion for anisotropic materials, 3 is Tsai- Hill criterion, 4 is Tsai- Wu criterion and 5 is <PERSON><PERSON> criterion. 11 and larger indicates a user- defined failure criterion. It is assumed that the number of strength constants will not be greater than 9 for a material. If the material is isotropic, the failure criterion and corresponding strength constants are defined as follows:", "page_idx": 5}, {"type": "text", "text": "If failure_criterion is 1, the max principal stress criterion is used and two strength constants are needed: one for tensile strength  \\((X)\\)  and one for compressive strength  \\((X^{\\prime})\\)  , arranged as  \\(X,X^{\\prime}\\)  . If failure_criterion is 2, the max principal strain criterion is used and two strength constants in terms of strains are needed: one for tensile strength  \\((X_{\\epsilon})\\)  and one for compressive strength", "page_idx": 5}, {"type": "text", "text": "\\((X_{\\epsilon}^{\\prime})\\)  , arranged as  \\(X_{\\epsilon},X_{\\epsilon}^{\\prime}\\)", "page_idx": 6}, {"type": "text", "text": ". If failure_criterion is 3, the max shear stress criterion (aka Tresca criterion) is used and one shear strength constant (  \\(S\\)  ) is needed.", "page_idx": 6}, {"type": "text", "text": "If failure_criterion is 4, the max shear strain criterion is used and one shear strength constant in terms of strains  \\((S_{\\epsilon})\\)  is needed.", "page_idx": 6}, {"type": "text", "text": "If failure_criterion is 5, the <PERSON><PERSON> criterion is used and one strength constant  \\((X)\\)  is needed.", "page_idx": 6}, {"type": "text", "text": "If the material is not isotropic (transversely isotropic, orthotropic, or general anisotropic), the failure criteria and corresponding strength constants are defined as follows:", "page_idx": 6}, {"type": "text", "text": "If failure_criterion is 1, the max stress criterion is used and nine strength constants are needed: tensile strengths  \\((X,Y,Z)\\)  in three directions, compressive strengths  \\((X^{\\prime},Y^{\\prime},Z^{\\prime})\\)  in three directions, and shear strengths  \\((R,T,S)\\)  in three principal planes, arranged as  \\(X,Y,Z,X^{\\prime},Y^{\\prime},Z^{\\prime},R,T,S\\)", "page_idx": 6}, {"type": "text", "text": "If failure_criterion is 2, the max strain criterion is used and nine strength constants in terms of strains are needed: tensile strengths  \\((X_{\\epsilon},Y_{\\epsilon},Z_{\\epsilon})\\)  in three directions, compressive strengths  \\((X_{\\epsilon}^{\\prime},Y_{\\epsilon}^{\\prime},Z_{\\epsilon}^{\\prime})\\)  in three directions, and shear strengths  \\((R_{\\epsilon},T_{\\epsilon},S_{\\epsilon})\\)  in three principal planes, arranged as  \\(X_{\\epsilon},Y_{\\epsilon},Z_{\\epsilon},X_{\\epsilon}^{\\prime},Y_{\\epsilon}^{\\prime},Z_{\\epsilon}^{\\prime},R_{\\epsilon},T_{\\epsilon},S_{\\epsilon}\\)", "page_idx": 6}, {"type": "text", "text": "If failure_criterion is 3, the Tsai- Hill criterion is used and six strength constants are needed: normal strengths in three directions and shear strengths in three principal planes, arranged as  \\(X,Y,Z,R,T,S\\)", "page_idx": 6}, {"type": "text", "text": "If failure_criterion is 4, the Tsai- Wu criterion is used and nine strength constants are needed: tensile strengths  \\((X,Y,Z)\\)  , compressive strengths  \\((X^{\\prime},Y^{\\prime},Z^{\\prime})\\)  in three directions, and shear strengths  \\((R,T,S)\\)  in three principal planes, arranged as  \\(X,Y,Z,X^{\\prime},Y^{\\prime},Z^{\\prime},R,T,S\\)", "page_idx": 6}, {"type": "text", "text": "If failure_criterion is 5, the <PERSON><PERSON> criterion is used and six strength constants are needed: tensile strengths  \\((X,Y)\\)  , compressive strengths  \\((X^{\\prime},Y^{\\prime})\\)  in two directions, and shear strengths  \\((R,S)\\)  in two principal planes, arranged as  \\(X,Y,X^{\\prime},Y^{\\prime},R,S\\)", "page_idx": 6}, {"type": "text", "text": "It is noted that for failure analyses, general anisotropic materials are also approximated using orthotropic materials due to limited number of strength constants. In VABS, both the tensile strengths and compressive strengths are expressed using positive numbers. In other words, in the uniaxial compressive test along  \\(y_{1}\\)  direction,  \\(\\sigma_{11} = - X^{\\prime}\\)  when material fails.", "page_idx": 6}, {"type": "text", "text": "The above block of data for strength properties for each material only needed if analysis=3. If analysis=1 or 2, the strength properties are not needed and should not be provided in the dehomogenization input file. Only the global beam responses as explained below should be stored in the dehomogenization input file.", "page_idx": 6}, {"type": "text", "text": "The rest of inputs in the dehomogenization input file contains the global beam responses obtained from the 1D global beam analysis. To carry out a dehomogenization analysis based on the", "page_idx": 6}, {"type": "text", "text": "classical model, VABS requires the following data:", "page_idx": 7}, {"type": "equation", "text": "\n\\[\n\\begin{array}{c c c}{{u_{1}}}&{{u_{2}}}&{{u_{3}}}\\\\ {{C_{11}}}&{{C_{12}}}&{{C_{13}}}\\\\ {{C_{21}}}&{{C_{22}}}&{{C_{23}}}\\\\ {{C_{31}}}&{{C_{32}}}&{{C_{33}}}\\\\ {{F_{1}}}&{{M_{1}}}&{{M_{2}}}&{{M_{3}}}\\end{array}\n\\]\n", "text_format": "latex", "page_idx": 7}, {"type": "text", "text": "where  \\(u_{1}\\) ,  \\(u_{2}\\) , and  \\(u_{3}\\)  are the 1D beam displacements along  \\(x_{1}\\) ,  \\(x_{2}\\) ,  \\(x_{3}\\) , respectively. The matrix  \\(C_{ij}\\) , with  \\(i = 1,2,3\\)  and  \\(j = 1,2,3\\) , is the direction cosine matrix defined as", "page_idx": 7}, {"type": "equation", "text": "\n\\[\n\\mathbf{B}_{i} = C_{i1} \\mathbf{b}_{1} + C_{i2} \\mathbf{b}_{2} + C_{i3} \\mathbf{b}_{3} \\text{with} i = 1,2,3\n\\]\n", "text_format": "latex", "page_idx": 7}, {"type": "text", "text": "where  \\(\\mathbf{B}_{1}\\) ,  \\(\\mathbf{B}_{2}\\) , and  \\(\\mathbf{B}_{3}\\)  are the base vectors of the deformed beam and  \\(\\mathbf{b}_{1}\\) ,  \\(\\mathbf{b}_{2}\\) , and  \\(\\mathbf{b}_{3}\\)  are the base vectors of the undeformed beam. Details of this definition can be found in Ref. [7].  \\(u_{i}\\)  and  \\(C_{ij}\\)  are needed only for recovering 3D displacements. If the user is not interested in 3D displacements, these values can be arbitrary real numbers.  \\(F_{1}\\)  is the axial force,  \\(M_{1}\\)  is the torque,  \\(M_{2}\\)  is the bending moment around  \\(x_{2}\\) , and  \\(M_{3}\\)  is the bending moment around  \\(x_{3}\\) . The sectional stress resultants are needed for computing 3D stresses/strains/failure indexes/strength ratios within the cross- section. For example, if the user wants to compute these quantities under 1 unit tensile axial force along with 1 unit bending moment around  \\(x_{2}\\) , the inputs can be arranged as:", "page_idx": 7}, {"type": "equation", "text": "\n\\[\n\\begin{array}{c c c}{{0}}&{{0}}&{{0}}\\\\ {{1}}&{{0}}&{{0}}\\\\ {{0}}&{{1}}&{{0}}\\\\ {{0}}&{{0}}&{{1}}\\\\ {{1}}&{{0}}&{{1}}&{{0}}\\end{array}\n\\]\n", "text_format": "latex", "page_idx": 7}, {"type": "text", "text": "To perform dehomogenization for multiple load cases, the user needs to insert corresponding lines of  \\(F_{1}, M_{1}, M_{2}, M_{3}\\)  after the end of this block. For example, to perform dehomogenization for two more load cases with  \\(F_{1} = 2\\) ,  \\(M_{1} = 2\\) ,  \\(M_{2} = M_{3} = 0\\)  and  \\(F_{1} = 2\\) ,  \\(M_{1} = 3\\) ,  \\(M_{3} = 4\\) ,  \\(M_{5} = 5\\) , we must provide the following inputs.", "page_idx": 7}, {"type": "equation", "text": "\n\\[\n\\begin{array}{c c c}{{0}}&{{0}}&{{0}}\\\\ {{1}}&{{0}}&{{0}}\\\\ {{0}}&{{1}}&{{0}}\\\\ {{0}}&{{0}}&{{1}}\\\\ {{1}}&{{0}}&{{1}}&{{0}}\\\\ {{2}}&{{2}}&{{0}}&{{0}}\\\\ {{2}}&{{3}}&{{4}}&{{5}}\\end{array}\n\\]\n", "text_format": "latex", "page_idx": 7}, {"type": "text", "text": "To carry out a dehomogenization analysis based on the <PERSON><PERSON><PERSON> model, VABS requires the", "page_idx": 7}, {"type": "text", "text": "following data:", "page_idx": 8}, {"type": "equation", "text": "\n\\[\n\\begin{array}{r l}{u_{1}} & {u_{2}} & {u_{3}}\\\\ {C_{11}} & {C_{12}} & {C_{13}}\\\\ {C_{21}} & {C_{22}} & {C_{23}}\\\\ {C_{31}} & {C_{32}} & {C_{33}}\\\\ {F_{1}} & {M_{1}} & {M_{2}} & {M_{3}}\\\\ {F_{2}} & {F_{2}} & {}\\\\ {f_{1}} & {f_{2}} & {f_{3}} & {m_{1}} & {m_{2}} & {m_{3}}\\\\ {f_{1}^{\\prime}} & {f_{2}^{\\prime}} & {f_{3}^{\\prime}} & {m_{1}^{\\prime}} & {m_{2}^{\\prime}} & {m_{3}^{\\prime}}\\\\ {f_{1}^{\\prime \\prime}} & {f_{2}^{\\prime \\prime}} & {f_{3}^{\\prime \\prime}} & {m_{1}^{\\prime \\prime}} & {m_{2}^{\\prime \\prime}} & {m_{3}^{\\prime \\prime}}\\\\ {f_{1}^{\\prime \\prime \\prime}} & {f_{2}^{\\prime \\prime \\prime}} & {f_{3}^{\\prime \\prime \\prime}} & {m_{1}^{\\prime \\prime \\prime}} & {m_{2}^{\\prime \\prime \\prime}} & {m_{3}^{\\prime \\prime \\prime}}\\\\ {f_{1}^{\\prime \\prime \\prime}} & {f_{2}^{\\prime \\prime \\prime}} & {f_{3}^{\\prime \\prime \\prime}} & {m_{1}^{\\prime \\prime \\prime}} & {m_{2}^{\\prime \\prime \\prime}} & {m_{3}^{\\prime \\prime \\prime}} \\end{array}\n\\]\n", "text_format": "latex", "page_idx": 8}, {"type": "text", "text": "where the additional data  \\(F_{2}\\)  and  \\(F_{3}\\)  are transverse shear forces along  \\(x_{2}\\)  and  \\(x_{3}\\)  , respectively.  \\(f_{1},f_{2},f_{3}\\)  are distributed forces (including both applied forces and inertial forces) per unit span along  \\(x_{1},x_{2},x_{3}\\)  respectively.  \\(m_{1},m_{2},m_{3}\\)  are distributed moments (including both applied and inertial moments) per unit span along  \\(x_{1},x_{2},x_{3}\\)  respectively. The prime denotes derivative with respect to beam axis, that is  \\(\\begin{array}{r}{()^{\\prime} = \\frac{\\partial}{\\partial x_{1}}} \\end{array}\\) \\(\\begin{array}{r}{()^{\\prime \\prime} = \\frac{\\partial^{\\prime}}{x_{1}^{\\prime}}} \\end{array}\\)  , and  \\(\\begin{array}{r}{()^{\\prime \\prime \\prime} = \\frac{\\partial^{3}}{x_{1}^{3}}} \\end{array}\\)  . If nload > 1, at the end of the above data block, we need to append two lines (one line for  \\(F_{1},M_{1},M_{2},M_{3}\\)  and one line for  \\(F_{2},F_{3}\\)  for each load case.", "page_idx": 8}, {"type": "text", "text": "To carry out a dehomogenization analysis based on the <PERSON><PERSON>ov model, VABS requires the following data:", "page_idx": 8}, {"type": "equation", "text": "\n\\[\n\\begin{array}{r l r l r l r l r l} & {u_{1}} & {u_{2}} & {u_{3}} & & & & \\\\ & {C_{11}} & {C_{12}} & {C_{13}} & & & & \\\\ & {C_{21}} & {C_{22}} & {C_{23}} & & & & \\\\ & {C_{31}} & {C_{32}} & {C_{33}} & & & & \\\\ & {\\bar{\\gamma}_{11}} & {\\bar{\\kappa}_{1}} & {\\bar{\\kappa}_{2}} & {\\bar{\\kappa}_{3}} & {\\bar{\\kappa}_{1}^{\\prime}} & {\\bar{\\kappa}_{1}^{\\prime \\prime}} & {\\bar{\\kappa}_{1}^{\\prime \\prime \\prime}} & \\\\ & {\\bar{\\gamma}_{11}} & {\\bar{\\kappa}_{1}} & {\\bar{\\kappa}_{2}} & {\\bar{\\kappa}_{3}} & {\\bar{\\kappa}_{3}} & {\\bar{\\kappa}_{1}^{\\prime}} & {\\bar{\\kappa}_{1}^{\\prime \\prime}} & \\end{array}\n\\]\n", "text_format": "latex", "page_idx": 8}, {"type": "text", "text": "where  \\(\\bar{\\gamma}_{11}\\)  is the beam axial strain,  \\(\\bar{\\kappa}_{1}\\)  is the twist ,  \\(\\bar{\\kappa}_{2}\\)  and  \\(\\bar{\\kappa}_{3}\\)  are the curvatures around  \\(x_{2}\\)  and  \\(x_{3}\\)  respectively.", "page_idx": 8}, {"type": "text", "text": "It is noted that the global behavior needed for dehomogenization analyses should not violate the small strain assumption. Otherwise, you might get some unexpected results. For example, if your transverse shear stiffness is 2.5 N, then inputting a shear force resultant of 1 N is too large as the shear strain will be about 0.4, which cannot be considered as small, the basic assumption of the VABS theory.", "page_idx": 8}, {"type": "text", "text": "Both input files, input_file_name and input_file_name.glb, should be ended with a blank line to avoid any possible incompatibility of different computer operating systems. The input file can be given any name as long as the total number of the characters of the name including extension is not more than 256. For the convenience of the user to identify mistakes in the input file, all the inputs are echoed in the file named input_file_name.ech. Error messages are also written at the end of input_file_name.ech and on the output screen.", "page_idx": 8}, {"type": "text", "text": "7.3 User-defined Failure Criterion", "text_level": 2, "page_idx": 8}, {"type": "text", "text": "A simple UMAT is provided for users to program their own failure criterion by changing the fortran code UserFC.f90. The sample code is for max stress failure criterion for anisotropic materials. The", "page_idx": 8}, {"type": "text", "text": "failure criterion number and strength constants should be provided as described above. Pointwise strains and stresses and strength constants are passed to the user subroutine, failure index, strength ratio, failure mode, and strength ratio for each stress component are computed inside this subroutine and passed back to VABS. Users need to first modify UserFC.f90 according to their own failure criterion, then compile it to be a shared library. A sample make file (MakeUser) for using the fortran compiler fort is included. One only needs to execute make - f MakeUser to compile the user subroutine. Then, one can use VABS with user- defined failure criterion. The code is simple enough and there is enough comments inside the source codes for the user to adopt the sample code for other user- defined failure criterion.", "page_idx": 9}, {"type": "text", "text": "8 VABS Outputs", "text_level": 1, "page_idx": 9}, {"type": "text", "text": "VABS homogenization analysis outputs the sectional properties stored in a text file named input_file_name.K. VABS dehomogenization analysis could output 3D displacement/strain/stress, or failure index/strength ratio distributions over the cross- section in different files as explained later. All these output files are in pure text format and can be opened by any text editor.", "page_idx": 9}, {"type": "text", "text": "8.1 Homogenization Outputs", "text_level": 2, "page_idx": 9}, {"type": "text", "text": "Sectional properties obtained by a VABS homogenization analysis are stored in input_file_name.K. Some results are listed as individual numbers, and some are listed as matrices. The definitions of these properties are briefly summarized here for the convenience of end users. For more details, please refer to VABS related publications.", "page_idx": 9}, {"type": "text", "text": "VABS first computes the inertial properties which is represented by a  \\(6\\times 6\\)  mass matrix with respect to the beam coordinate system. The elements of the mass matrix are arranged as", "page_idx": 9}, {"type": "equation", "text": "\n\\[\n\\left[ \\begin{array}{c c c c c c}{\\mu} & 0 & 0 & 0 & {\\mu x_{M3}} & {-\\mu x_{M2}}\\\\ 0 & \\mu & 0 & {-\\mu x_{M3}} & 0 & 0\\\\ 0 & 0 & \\mu & {\\mu x_{M2}} & 0 & 0\\\\ 0 & {-\\mu x_{M3}} & {\\mu x_{M2}} & {i_{22} + i_{33}} & 0 & 0\\\\ {\\mu x_{M3}} & 0 & 0 & 0 & {i_{22}} & {i_{23}}\\\\ {-\\mu x_{M2}} & 0 & 0 & 0 & {i_{23}} & {i_{33}} \\end{array} \\right]\n\\]\n", "text_format": "latex", "page_idx": 9}, {"type": "text", "text": "where  \\(\\mu\\)  is mass per unit length,  \\(x_{M2}\\)  and  \\(x_{M3}\\)  are the two coordinates of the mass center (also called the mass- weighted centroid), and  \\(i_{22}, i_{23}\\)  and  \\(i_{33}\\)  are the second mass moments of inertia. The mass center and mass moments of inertia are measured with respect to the origin  \\(O\\)  and coordinate axes  \\(x_{2}\\)  and  \\(x_{3}\\) . VABS also outputs the mass center, the mass matrix measured with respect to the coordinate system with the mass center as the origin and coordinates parallel to the beam coordinate system. Furthermore, VABS also outputs the inertial properties with respect to the principal inertial coordinate system (origin at the mass center, coordinates aligning with the principal inertial axes) including a mass per unit length, mass moments of inertia about the three axes, the orientation of the principal inertial axes, and mass- weighted radius of gyration (defined as the square root of the mass moment of inertia about  \\(x_{1}\\)  divided by the mass per unit length).", "page_idx": 9}]