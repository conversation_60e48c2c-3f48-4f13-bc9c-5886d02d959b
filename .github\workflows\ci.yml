# name: CI

# on:
#   push:
#     branches: [ main, develop ]
#   pull_request:
#     branches: [ main, develop ]

# jobs:
#   test:
#     runs-on: ${{ matrix.os }}
#     strategy:
#       matrix:
#         os: [ubuntu-latest, windows-latest, macos-latest]
#         python-version: ["3.13"]

#     steps:
#     - uses: actions/checkout@v4

#     - name: Install uv
#       uses: astral-sh/setup-uv@v3
#       with:
#         version: "latest"

#     - name: Set up Python ${{ matrix.python-version }}
#       run: uv python install ${{ matrix.python-version }}

#     - name: Install dependencies
#       run: uv sync --all-extras --dev

#     - name: Run linting
#       run: |
#         uv run flake8 src tests
#         uv run black --check src tests
#         uv run isort --check-only src tests

#     - name: Run type checking
#       run: uv run mypy src

#     - name: Run tests
#       run: uv run pytest tests/ -v --cov=src/pyvabs --cov-report=xml

#     - name: Upload coverage to Codecov
#       uses: codecov/codecov-action@v3
#       with:
#         file: ./coverage.xml
#         flags: unittests
#         name: codecov-umbrella
#         fail_ci_if_error: false

#   docs:
#     runs-on: ubuntu-latest
#     steps:
#     - uses: actions/checkout@v4

#     - name: Install uv
#       uses: astral-sh/setup-uv@v3
#       with:
#         version: "latest"

#     - name: Set up Python
#       run: uv python install 3.13

#     - name: Install dependencies
#       run: uv sync --group docs

#     - name: Build documentation
#       run: |
#         cd docs
#         uv run sphinx-build -b html . _build/html

#     - name: Upload docs artifact
#       uses: actions/upload-artifact@v3
#       with:
#         name: documentation
#         path: docs/_build/html
