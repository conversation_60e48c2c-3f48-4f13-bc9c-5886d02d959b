../../Scripts/pre-commit.exe,sha256=ZifS6zomXnwE57xeBDyVWXww9BH2-m0fje7DnWGN_Rk,41983
pre_commit-4.2.0.dist-info/INSTALLER,sha256=5hhM4Q4mYTT9z6QB6PGpUAW81PGNFrYrdXMj4oM_6ak,2
pre_commit-4.2.0.dist-info/LICENSE,sha256=6iyifLp8w1gi2VpG1ZvNPMiOGWWS5jkNGUmjWf_JkOg,1092
pre_commit-4.2.0.dist-info/METADATA,sha256=Ek2YBYPkLoKcPLLrolAJr0fZAnKpv2tZeg1wCqSjsdI,1277
pre_commit-4.2.0.dist-info/RECORD,,
pre_commit-4.2.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pre_commit-4.2.0.dist-info/WHEEL,sha256=DZajD4pwLWue70CAfc7YaxT1wLUciNBvN_TTcvXpltE,110
pre_commit-4.2.0.dist-info/entry_points.txt,sha256=9QCW6lC130XmXhOB-3XA71fK9Ef5R5YlUma7E93lrPc,52
pre_commit-4.2.0.dist-info/top_level.txt,sha256=KMCc5TrDQcN3MCxwR72948Y2zIrILKmtnSAdqWBUa_I,11
pre_commit/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pre_commit/__main__.py,sha256=uPnSXQQC50TKhEO4moFj7DjjJ3xrVVJpcYpD7JkA9gc,127
pre_commit/all_languages.py,sha256=MhTZG2-XhcmupHdNeKR2VjQX_GXynZzbVC9fcUTHX84,1412
pre_commit/clientlib.py,sha256=_Rslx40nVIp4HWinOA1z-PvFJave9xeBc9OD9SrmPYM,15282
pre_commit/color.py,sha256=WzrGPdQevNxYSx3howvTlGG--uZMjhdgCPvtALpvGVQ,3219
pre_commit/commands/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pre_commit/commands/autoupdate.py,sha256=j1MAsszreh_1iYNp2epNVFmhgrwq2TwaI9h_-svG5EA,7157
pre_commit/commands/clean.py,sha256=emsIYwprEt4LmcRgFuTSurwgAo5aqwStHylW1tGFSsY,429
pre_commit/commands/gc.py,sha256=bX7QcgPMhv4_ZmJgHek2UIA3W5N5jw9yYt5wMTTcgq4,2804
pre_commit/commands/hook_impl.py,sha256=7pk30p4jxGiwCJduvY1igjNJuZ8_SgTZNXoN3MFp7uw,9400
pre_commit/commands/init_templatedir.py,sha256=jFDNEUx51k5KX1ADbWTFzivGj_9f7SRq1nnb8uFo37U,1135
pre_commit/commands/install_uninstall.py,sha256=VoJZ18lRghIadh7IJRl1BLq7feI5DbzZwlIV3_uz4ZQ,5341
pre_commit/commands/migrate_config.py,sha256=WRzafGK7-BcTihWgyp8VUnAD8dw8-BVyuRyDF-arz9M,4154
pre_commit/commands/run.py,sha256=lYLMplALZJg55wo2MVsx8gbEsKp84Is_uw9FAf89vlg,14115
pre_commit/commands/sample_config.py,sha256=pR9SRhdjl05cqOLTkc8b8Grse8B_tGCCVZqAJcv3nRk,453
pre_commit/commands/try_repo.py,sha256=AZ8-JdITKeSnqajT4E6y6q2ROERmM1ppBw5Vz4t6bXw,2578
pre_commit/commands/validate_config.py,sha256=prustTJy8a9DI1m8RGaopiy_tf6YUp9Eyx_u4ep9Qts,371
pre_commit/commands/validate_manifest.py,sha256=cOl6ua8aIHu0Pa5tC-1c_P4ObnG8Y7m2sAaLu9zB3F4,377
pre_commit/constants.py,sha256=8DVzmf1Z_uEpiZll873n6YJB4oRIHt87lM3US4CqKvY,282
pre_commit/envcontext.py,sha256=2jtR6ozx8azT-lANC-OuNTO5CojRKpro5OqeDAFm5sQ,1593
pre_commit/error_handler.py,sha256=f-YwuV3GW1SEQJdftfVAqulONn3AyA9fHnni-jg1jvo,2621
pre_commit/errors.py,sha256=XxCmTt7WTCv70fF2XA6yktoE3zjkpDE8LJrXM9sCuYU,78
pre_commit/file_lock.py,sha256=Gx3BSIVQ66ZqqhiGKfbVA3wB5B00KM0qnsYPhD3zr6E,2342
pre_commit/git.py,sha256=UGSg9fdjbFPCtM2Wsdk0p45xfVMJo1NgfpAxfPZs4BA,8524
pre_commit/hook.py,sha256=a66O4OI4uEDMS_sDna_oKnyN8zf1hS99j4yhmv0kdqI,1513
pre_commit/lang_base.py,sha256=oFLiDPHNJM62w4p7jmRZNhVZEbsFS-8pkpWBK-9czCc,5238
pre_commit/languages/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pre_commit/languages/conda.py,sha256=XKZVBtNs-PS929EMH303vM4iYdyONfcsQXfyKpewdm0,2421
pre_commit/languages/coursier.py,sha256=jVK8aMxGdVN-gopf0JlTLc0viMrHYolSKvC_rSR5N0o,2508
pre_commit/languages/dart.py,sha256=J8LMuAD7nQ6Q1Pn8HaDSWQmrri8jxHCBbSnngBnlBeQ,3118
pre_commit/languages/docker.py,sha256=PJ8JVL4DZA3gU7VXMfVZb5-p3kxG29UOjfU1kJqzb3M,4840
pre_commit/languages/docker_image.py,sha256=tzUiZjADKhpocwKoMDzAYwblV4Bqg45s4z0Jp-cpoK0,847
pre_commit/languages/dotnet.py,sha256=e190Qjdreyerq52TjPt1yYNS9DMbh9pajt6JROt2bm0,3463
pre_commit/languages/fail.py,sha256=52hRwZYy0s9ICuB-lT321z6QETnGcwXMEMN24_5q2p4,685
pre_commit/languages/golang.py,sha256=v0XGvcYbCCG0q0JN3-vzWoB0I_XVu5niV-NRGAlhUAc,4658
pre_commit/languages/haskell.py,sha256=y2wYjQair58TjIWl0p5CWuPUM3UNCnOp_AuZ2P6Z9Rw,1662
pre_commit/languages/julia.py,sha256=Vbu_xgKfqOXp-B-y4ttUsu3NexoT5lBwPfxastBpQX8,4378
pre_commit/languages/lua.py,sha256=Y1JDnE0cQBoYgfkQ7uXbUTtUsoPD_2GLiZHos5ZOaaY,2535
pre_commit/languages/node.py,sha256=zPfCk4PtBI27Wcg0fWM_4-2lbWaw96xyG-w6ZFKEww4,3853
pre_commit/languages/perl.py,sha256=DatBJxV8gtvYaCMwUefrvURk3l5G7h_6OGuP2CEVQhM,1491
pre_commit/languages/pygrep.py,sha256=JXTKuPPJyFLXx8wK7zwHx5ELZXRzBVITU6VYUuIb7U8,3816
pre_commit/languages/python.py,sha256=0MjdMkEJZ_zE7kEhQajYz7Y94Vexx2dSvaJ-Q6fyxSs,7320
pre_commit/languages/r.py,sha256=f5DCQxeheC0SOu6DOQBLjLbyq2X18Yg78D0h6Vws2k8,7901
pre_commit/languages/ruby.py,sha256=VX1ha8QSSJ88WJtrB3ccSc3gEj8_Mio2QCI5PeV__EQ,4636
pre_commit/languages/rust.py,sha256=XuEW01Crv6jfjNFGjHSHF2xRgtv33hstshtW_XzEyc4,5492
pre_commit/languages/script.py,sha256=hHIqfZfoJReANiDI0mDVLhSfqk7QLrJKapcWnt2idAE,786
pre_commit/languages/swift.py,sha256=80WKNNZaX6TqfvcZGIUimD_g8MUFBQSnZNTNjn_SlrY,1605
pre_commit/languages/system.py,sha256=cMeR-yXRE-dsPaybIYWjD1_ZBRa7eTdyUJnsyGL_tq4,300
pre_commit/logging_handler.py,sha256=Yl_rj7peBY23rswiddSlTnpgYMMD0x9Tw41EK8nb3G4,1019
pre_commit/main.py,sha256=rncnZ-L-TDJ8VUpyn0mP-m0fx0cfy68vEjACoh2DsBo,15564
pre_commit/meta_hooks/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pre_commit/meta_hooks/check_hooks_apply.py,sha256=4e_D4eLrs45ZF_Ggoc0rqa9Rb6jh7dyUeQX4nItjE9k,1207
pre_commit/meta_hooks/check_useless_excludes.py,sha256=yFUoTJeipbG9rNQ6gusdzYFrGppw2mewZ6N_6fY-qNQ,2553
pre_commit/meta_hooks/identity.py,sha256=7RzSVW2tiVL4lEvft9uaa_ntuR7gPx4F5Ciqe_0X80Q,346
pre_commit/output.py,sha256=2Z0iN6P7qCu-fJf-vuI4CvC11z9HFVOzKBRD3krtV7o,911
pre_commit/parse_shebang.py,sha256=6_KS-obQ_iFlLa1r9SByMkwvWew5rMXMGYgXjB-qfjc,2481
pre_commit/prefix.py,sha256=wj3T2eiHeOp7G6UbF8v1pfC1kDhhsUranKlEUfMW3uk,495
pre_commit/repository.py,sha256=kRYFKOjOZQolAcO_SzSzSuiEAudrTgH4wo2M3cHMZiY,7608
pre_commit/resources/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pre_commit/resources/empty_template_.npmignore,sha256=zbyuFRBda3geYggTx5x-hodA1OnMU85vX8u8Ejh630s,2
pre_commit/resources/empty_template_Cargo.toml,sha256=rjclCuspy_DoGro5K_Yx-cjKO88rvQU5qkkbAYIiatg,96
pre_commit/resources/empty_template_LICENSE.renv,sha256=vJiU4spBSQJkgro6DzPpoTeFvYYGMdkKEhZQQh54scE,1052
pre_commit/resources/empty_template_Makefile.PL,sha256=oNZjJpS1Ba38XJ4VmQArf7ZfaSFbD4vDFNZ8ERoAy2A,104
pre_commit/resources/empty_template_activate.R,sha256=ydOQGgFscRyMU4bIqm1TzGQN8Dxfgc79eOXujP7uVjE,12037
pre_commit/resources/empty_template_environment.yml,sha256=SBs7AN3M19I6EX_lrIDhFavgAaBRubazR6imzTbhBCI,302
pre_commit/resources/empty_template_go.mod,sha256=35v54fbcB8Hs9TOPi6uHNiu9GTtuYs4M3ofWXpdG3j8,43
pre_commit/resources/empty_template_main.go,sha256=VaYLuXFRsrS2gEYkR85g7DRRGxT6ENd0QMl7l3cQFWY,29
pre_commit/resources/empty_template_main.rs,sha256=U25Qa7kJFMJDoSs5e5qZj4WuLL2boC39A6nhVcpcoPQ,13
pre_commit/resources/empty_template_package.json,sha256=iBN43GUUn05DYKA7vpOcdU27H-nTsr6aVFCJVq4G3-E,73
pre_commit/resources/empty_template_pre-commit-package-dev-1.rockspec,sha256=Jd8cePFPSzN0YO5T7vZLAVTX_KrXQiSNu5uFQXmETEU,212
pre_commit/resources/empty_template_pre_commit_placeholder_package.gemspec,sha256=UCWGrtzSq_bUTvDgJuV94ZPQ6KHrGbFywWyppbR8ZIQ,195
pre_commit/resources/empty_template_pubspec.yaml,sha256=cSn_jSXaYL5L2N9UtZblUumra-pehiTuJ-lwJwGxPQU,78
pre_commit/resources/empty_template_renv.lock,sha256=Oa6eSaJs4lZn8A4qo1OkEvDC0H_g-2VrmzFuATmMhuM,351
pre_commit/resources/empty_template_setup.py,sha256=yAcMxdYfxYLOEj6h1xcPC0MHX-LzTs7Eq7-sjL59aWo,93
pre_commit/resources/hook-tmpl,sha256=OJybNgJvZYMlvJbUZkzzdGDlxBXVMFmiIb0DYCP3b_U,528
pre_commit/resources/rbenv.tar.gz,sha256=EfzijkoqjQUhQF-IXKhFBCgUz82K81ZIM3HfktZDYq8,32545
pre_commit/resources/ruby-build.tar.gz,sha256=-u_7t7h9Zb_dgQqBKWaVv1j6fsEQO5Qa4_oyZU79A3Y,88488
pre_commit/resources/ruby-download.tar.gz,sha256=mU_7nLFc-7FEbf9OCNVtHGlKGNhe6uGZaLNKeV4qnCc,5269
pre_commit/staged_files_only.py,sha256=oMWqz7-qAt5DcKLp0u4fCSAWyDBKwtxsSk1EURWKq5Y,4155
pre_commit/store.py,sha256=NtUD7gnkhhn9GsRyshjakmy4XKgAlqEV6FzVx_96lwY,9392
pre_commit/util.py,sha256=KHu78C4SZ09anYQXzp8PwmMQQ_Hpwwf1yj3mA4z_udA,7038
pre_commit/xargs.py,sha256=M1w8-eFiBBAYt3wxNnPnBKOQGnJ-hjAWHzyVmlCXbM8,5541
pre_commit/yaml.py,sha256=61sHsBF4ZGPugQUXGih8L6a5gTzgsD_X9wYibDlN560,561
pre_commit/yaml_rewrite.py,sha256=ICKYxX8Ku54Ci9guVS2J1J2H6ErdqEMKHZm5PbR1WQk,1337
