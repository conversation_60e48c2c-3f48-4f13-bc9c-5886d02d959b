# PyVabs Documentation

Welcome to PyVabs, a Python library for [add your description here].

## Quick Start

### Installation

```bash
pip install pyvabs
```

### Basic Usage

```python
import pyvabs

# Basic greeting
print(pyvabs.hello())

# Using the core functionality
from pyvabs.core import PyVabsCore

# Create an instance
core = PyVabsCore("my_instance")

# Process some data
data = ["item1", "item2", "item3"]
result = core.process(data)
print(result)
```

## Features

- **Core Functionality**: Main processing capabilities
- **Utilities**: Helper functions for common tasks
- **Type Safety**: Full type hints support
- **Well Tested**: Comprehensive test suite
- **Documentation**: Complete API documentation

## Table of Contents

```{toctree}
:maxdepth: 2
:caption: User Guide:

installation
quickstart
api/index
examples
```

```{toctree}
:maxdepth: 2
:caption: Developer Documentation:

../DEVELOPER
architecture
../CHANGELOG
```

## Indices and tables

- {ref}`genindex`
- {ref}`modindex`
- {ref}`search`
