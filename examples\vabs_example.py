#!/usr/bin/env python3
"""VABS Interface Example.

This example demonstrates how to use the PyVabs VABS interface functions
for homogenization and recovery analysis of beam cross-sections.

Note: This example shows the interface usage. The actual VABS computation
functions are not yet implemented and will return placeholder results.
"""

import numpy as np
from pyvabs.vabs_interface import (
    VABSHomogenizationResult,
    VABSRecoveryResult,
    constitutive_modeling,
    recovery,
    create_material_matrix,
    create_simple_mesh,
    parse_vabs_output_stress_strain,
    validate_homogenization_inputs,
    validate_recovery_inputs,
)


def create_sample_composite_layup():
    """Create a sample composite layup for demonstration."""
    print("Creating sample composite layup...")
    
    # Define layup: [0/90/0] laminate
    nlayer = 3
    layup_angles = np.array([0.0, 90.0, 0.0])  # degrees
    mat_type_layer = np.array([1, 1, 1], dtype=np.int32)  # All same material
    
    # Create carbon fiber material properties
    carbon_fiber = create_material_matrix(
        material_type='orthotropic',
        E1=150e9,    # 150 GPa fiber direction
        E2=10e9,     # 10 GPa transverse direction
        E3=10e9,     # 10 GPa through-thickness
        G12=5e9,     # 5 GPa in-plane shear
        G13=5e9,     # 5 GPa out-of-plane shear
        G23=3e9,     # 3 GPa transverse shear
        nu12=0.3,    # Poisson's ratio
        nu13=0.3,
        nu23=0.4
    )
    
    print(f"  Number of layers: {nlayer}")
    print(f"  Layup angles: {layup_angles}")
    print(f"  Material E1: {carbon_fiber[0]/1e9:.1f} GPa")
    print(f"  Material E2: {carbon_fiber[1]/1e9:.1f} GPa")
    
    return nlayer, layup_angles, mat_type_layer, carbon_fiber


def create_sample_mesh():
    """Create a sample mesh for the cross-section."""
    print("\nCreating sample rectangular cross-section mesh...")
    
    # Create a simple rectangular cross-section: 20mm x 2mm
    width = 0.020  # 20 mm
    height = 0.002  # 2 mm
    nx, ny = 4, 1  # 4 elements in width, 1 in height
    
    coords, elements = create_simple_mesh(width, height, nx, ny)
    
    nnode = coords.shape[0]
    nelem = elements.shape[0]
    
    print(f"  Cross-section: {width*1000:.1f} mm x {height*1000:.1f} mm")
    print(f"  Mesh: {nx} x {ny} elements")
    print(f"  Nodes: {nnode}, Elements: {nelem}")
    
    return coords, elements, nnode, nelem


def demonstrate_homogenization():
    """Demonstrate VABS homogenization analysis."""
    print("\n" + "="*60)
    print("VABS HOMOGENIZATION ANALYSIS EXAMPLE")
    print("="*60)
    
    # Create sample data
    nlayer, layup_angles, mat_type_layer, carbon_fiber = create_sample_composite_layup()
    coords, elements, nnode, nelem = create_sample_mesh()
    
    # Material properties
    nmate = 1
    material = carbon_fiber.reshape(1, -1)  # (nmate, 21)
    density = np.array([1600.0])  # kg/m³ for carbon fiber
    
    # Setup analysis parameters
    print("\nSetting up homogenization analysis...")
    
    # Create input arrays
    layup = np.zeros((nelem, 1))  # Simple layup (LAY_CONST=1)
    mat_type = np.ones(nelem, dtype=np.int32)  # All elements use material 1
    orth = np.array([1], dtype=np.int32)  # Orthotropic material
    damping = np.zeros(nmate)
    damping_layer = np.zeros(nlayer)
    
    # Analysis flags
    Timoshenko_I = 1  # Enable Timoshenko model
    curved_I = 0      # Straight beam
    oblique_I = 0     # No oblique coordinates
    trapeze_I = 0     # No trapeze effects
    Vlasov_I = 0      # No Vlasov model
    damping_I = 0     # No damping analysis
    
    # Initial conditions
    kb = np.zeros(3)    # No initial twist/curvature
    beta = np.zeros(3)  # No oblique parameters
    
    try:
        # Validate inputs first
        validate_homogenization_inputs(
            nnode=nnode, nelem=nelem, nmate=nmate, nlayer=nlayer,
            coord=coords, element=elements, material=material
        )
        print("✓ Input validation passed")
        
        # Call homogenization function
        print("Calling VABS constitutive modeling...")
        result = constitutive_modeling(
            inp_name="sample_beam.dat",
            format_I=1,
            mat_type_layer=mat_type_layer,
            layup_angle=layup_angles,
            LAY_CONST=1,
            nlayer=nlayer,
            Timoshenko_I=Timoshenko_I,
            curved_I=curved_I,
            oblique_I=oblique_I,
            trapeze_I=trapeze_I,
            Vlasov_I=Vlasov_I,
            damping_I=damping_I,
            kb=kb,
            beta=beta,
            nnode=nnode,
            nelem=nelem,
            nmate=nmate,
            coord=coords,
            element=elements,
            layup=layup,
            mat_type=mat_type,
            material=material,
            orth=orth,
            density=density,
            damping=damping,
            damping_layer=damping_layer
        )
        
        print(f"Analysis completed with message: {result.error}")
        
    except Exception as e:
        print(f"❌ Error in homogenization: {e}")


def demonstrate_recovery():
    """Demonstrate VABS recovery analysis."""
    print("\n" + "="*60)
    print("VABS RECOVERY ANALYSIS EXAMPLE")
    print("="*60)
    
    # Reuse setup from homogenization
    nlayer, layup_angles, mat_type_layer, carbon_fiber = create_sample_composite_layup()
    coords, elements, nnode, nelem = create_sample_mesh()
    
    # Material and mesh setup (same as homogenization)
    nmate = 1
    material = carbon_fiber.reshape(1, -1)
    density = np.array([1600.0])
    layup = np.zeros((nelem, 1))
    mat_type = np.ones(nelem, dtype=np.int32)
    orth = np.array([1], dtype=np.int32)
    damping = np.zeros(nmate)
    damping_layer = np.zeros(nlayer)
    
    # Analysis flags (same as homogenization)
    kb = np.zeros(3)
    beta = np.zeros(3)
    
    # Recovery-specific inputs
    print("\nSetting up recovery analysis...")
    
    # 1D beam solution (example values)
    disp_1D = np.array([0.001, 0.0, 0.0])  # 1mm axial displacement
    dir_cos_1D = np.eye(3)  # Identity direction cosine matrix
    force_1D = np.array([0.0, 0.0, 1000.0, 0.0, 0.0, 0.0])  # 1000 N axial force
    
    # Distributed loads (all zero for this example)
    load_1D = np.zeros(6)
    load1_1D = np.zeros(6)
    load2_1D = np.zeros(6)
    
    # Vlasov strain measures (zero for non-Vlasov analysis)
    strain_CL = np.zeros(4)
    strain_CL_1 = np.zeros(4)
    strain_CL_2 = np.zeros(4)
    
    try:
        # Validate inputs
        validate_recovery_inputs(
            nnode=nnode, nelem=nelem, nmate=nmate, nlayer=nlayer,
            recover_I=2, disp_1D=disp_1D, force_1D=force_1D,
            coord=coords, element=elements, material=material
        )
        print("✓ Input validation passed")
        
        # Call recovery function
        print("Calling VABS recovery analysis...")
        result = recovery(
            inp_name="sample_beam.dat",
            format_I=1,
            mat_type_layer=mat_type_layer,
            layup_angle=layup_angles,
            LAY_CONST=1,
            nlayer=nlayer,
            recover_I=2,  # Linear recovery
            Timoshenko_I=1,
            curved_I=0,
            oblique_I=0,
            Vlasov_I=0,
            damping_I=0,
            kb=kb,
            beta=beta,
            nnode=nnode,
            nelem=nelem,
            nmate=nmate,
            coord=coords,
            element=elements,
            layup=layup,
            mat_type=mat_type,
            material=material,
            orth=orth,
            density=density,
            damping=damping,
            damping_layer=damping_layer,
            disp_1D=disp_1D,
            dir_cos_1D=dir_cos_1D,
            strain_CL=strain_CL,
            strain_CL_1=strain_CL_1,
            strain_CL_2=strain_CL_2,
            force_1D=force_1D,
            load_1D=load_1D,
            load1_1D=load1_1D,
            load2_1D=load2_1D
        )
        
        print(f"Recovery completed with message: {result.error}")
        
    except Exception as e:
        print(f"❌ Error in recovery: {e}")


def demonstrate_output_parsing():
    """Demonstrate parsing of VABS output data."""
    print("\n" + "="*60)
    print("VABS OUTPUT PARSING EXAMPLE")
    print("="*60)
    
    # Create sample stress/strain data (as would be returned by VABS)
    print("Creating sample stress/strain output data...")
    
    n_points = 5
    ss_data = np.random.rand(n_points, 26)
    
    # Set position data (first 2 columns)
    ss_data[:, 0] = np.linspace(0, 0.02, n_points)  # x2 positions
    ss_data[:, 1] = np.linspace(0, 0.002, n_points)  # x3 positions
    
    # Parse in beam coordinates
    print("\nParsing stress/strain data in beam coordinates...")
    beam_results = parse_vabs_output_stress_strain(ss_data, 'beam')
    
    print(f"  Position data shape: {beam_results['position'].shape}")
    print(f"  Strain data shape: {beam_results['strain'].shape}")
    print(f"  Stress data shape: {beam_results['stress'].shape}")
    
    # Parse in material coordinates
    print("\nParsing stress/strain data in material coordinates...")
    material_results = parse_vabs_output_stress_strain(ss_data, 'material')
    
    print(f"  Position data shape: {material_results['position'].shape}")
    print(f"  Strain data shape: {material_results['strain'].shape}")
    print(f"  Stress data shape: {material_results['stress'].shape}")


def main():
    """Main example function."""
    print("PyVabs VABS Interface Example")
    print("This example demonstrates the Python interface to VABS functions.")
    print("Note: Actual VABS computations are not yet implemented.\n")
    
    try:
        # Demonstrate homogenization
        demonstrate_homogenization()
        
        # Demonstrate recovery
        demonstrate_recovery()
        
        # Demonstrate output parsing
        demonstrate_output_parsing()
        
        print("\n" + "="*60)
        print("EXAMPLE COMPLETED SUCCESSFULLY")
        print("="*60)
        print("\nNext steps:")
        print("1. Implement actual VABS library calls in the interface functions")
        print("2. Add error handling for VABS-specific errors")
        print("3. Extend with additional VABS features as needed")
        
    except Exception as e:
        print(f"\n❌ Example failed with error: {e}")
        raise


if __name__ == "__main__":
    main()
