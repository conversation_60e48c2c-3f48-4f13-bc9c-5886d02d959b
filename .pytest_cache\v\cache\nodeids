["tests/test_core.py::TestCreateInstance::test_create_instance_basic", "tests/test_core.py::TestCreateInstance::test_create_instance_with_kwargs", "tests/test_core.py::TestPyVabsCore::test_get_info", "tests/test_core.py::TestPyVabsCore::test_init_default", "tests/test_core.py::TestPyVabsCore::test_init_with_config", "tests/test_core.py::TestPyVabsCore::test_process", "tests/test_core.py::TestPyVabsCore::test_process_empty_list", "tests/test_core.py::test_fixture_usage", "tests/test_init.py::test_hello_function", "tests/test_init.py::test_package_all", "tests/test_init.py::test_package_author", "tests/test_init.py::test_package_email", "tests/test_init.py::test_package_version", "tests/test_utils.py::TestConfig::test_load_config_file_not_found", "tests/test_utils.py::TestConfig::test_load_config_invalid_json", "tests/test_utils.py::TestConfig::test_save_and_load_config", "tests/test_utils.py::TestListUtilities::test_chunk_empty_list", "tests/test_utils.py::TestListUtilities::test_chunk_list", "tests/test_utils.py::TestListUtilities::test_chunk_list_uneven", "tests/test_utils.py::TestListUtilities::test_flatten_empty_list", "tests/test_utils.py::TestListUtilities::test_flatten_list", "tests/test_utils.py::TestListUtilities::test_flatten_list_with_empty_sublists", "tests/test_utils.py::TestLogging::test_setup_logging_debug", "tests/test_utils.py::TestLogging::test_setup_logging_default", "tests/test_utils.py::TestValidation::test_validate_input_int", "tests/test_utils.py::TestValidation::test_validate_input_list", "tests/test_utils.py::TestValidation::test_validate_input_string"]