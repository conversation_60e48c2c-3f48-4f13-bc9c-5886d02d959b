{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.10.2", "globals": "af64fc1d98c65652aa2354644b03fe56", "files": {"z_772be134f360b8cf___init___py": {"hash": "a817b7abffc7c9b2c8570a7778ddfc6f", "index": {"url": "z_772be134f360b8cf___init___py.html", "file": "src\\pyvabs\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 8, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_772be134f360b8cf_core_py": {"hash": "79d2d933d7777fe030e6af0bb8c0d27a", "index": {"url": "z_772be134f360b8cf_core_py.html", "file": "src\\pyvabs\\core.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 11, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_772be134f360b8cf_utils_py": {"hash": "a3fb15adaca2469e2dccb9a0bb147e18", "index": {"url": "z_772be134f360b8cf_utils_py.html", "file": "src\\pyvabs\\utils.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 33, "n_excluded": 0, "n_missing": 3, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}