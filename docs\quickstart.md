# Developer Quick Start Guide

This guide helps developers get up and running with PyVabs development quickly.

## Prerequisites

- Python 3.13+
- [uv](https://docs.astral.sh/uv/) package manager
- Git

## 5-Minute Setup

### 1. <PERSON><PERSON> and Setup

```bash
# Clone the repository
git clone <repository-url>
cd pyvabs

# One-command setup
python scripts/setup_dev.py
```

### 2. Verify Installation

```bash
# Test the package
uv run python -c "import pyvabs; print(pyvabs.hello())"

# Run tests
uv run pytest tests/test_init.py -v

# Check code quality
uv run black --check src tests
uv run mypy src
```

### 3. Make Your First Change

```bash
# Create a feature branch
git checkout -b feature/my-feature

# Edit code (example: modify src/pyvabs/core.py)
# Add tests (example: modify tests/test_core.py)

# Run quality checks
python scripts/run_tests.py --all

# Commit changes
git add .
git commit -m "feat: add my awesome feature"
```

## Essential Commands

### Development Workflow

```bash
# Install/update dependencies
uv sync --extra dev

# Run all tests with coverage
uv run pytest --cov=src/pyvabs

# Format code
uv run black src tests
uv run isort src tests

# Lint code
uv run flake8 src tests

# Type check
uv run mypy src

# Run examples
uv run python examples/basic_usage.py
```

### Using Helper Scripts

```bash
# Setup development environment
python scripts/setup_dev.py

# Run tests with options
python scripts/run_tests.py --help
python scripts/run_tests.py --coverage
python scripts/run_tests.py --all
python scripts/run_tests.py tests/test_core.py
```

### Documentation

```bash
# Build documentation
cd docs
uv run sphinx-build -b html . _build/html

# View documentation
# Open docs/_build/html/index.html in browser
```

## Project Structure Overview

```
pyvabs/
├── src/pyvabs/          # 📦 Main package
│   ├── __init__.py      # Public API
│   ├── core.py          # Core functionality
│   └── utils.py         # Utilities
├── tests/               # 🧪 Test suite
├── docs/                # 📚 Documentation
├── examples/            # 💡 Usage examples
├── scripts/             # 🔧 Dev scripts
└── .github/workflows/   # 🚀 CI/CD
```

## Code Style Quick Reference

### Function Example

```python
def process_data(data: List[str], config: Optional[Dict[str, Any]] = None) -> List[str]:
    """Process input data according to configuration.
    
    Args:
        data: List of strings to process.
        config: Optional configuration dictionary.
        
    Returns:
        List of processed strings.
        
    Raises:
        ValueError: If data is empty.
    """
    if not data:
        raise ValueError("Data cannot be empty")
    
    config = config or {}
    prefix = config.get("prefix", "processed_")
    
    return [f"{prefix}{item}" for item in data]
```

### Class Example

```python
class DataProcessor:
    """Processes data according to configuration.
    
    Attributes:
        name: The processor name.
        config: Configuration dictionary.
    """
    
    def __init__(self, name: str, config: Optional[Dict[str, Any]] = None) -> None:
        """Initialize the processor.
        
        Args:
            name: The processor name.
            config: Optional configuration.
        """
        self.name = name
        self.config = config or {}
```

### Test Example

```python
class TestDataProcessor:
    """Test cases for DataProcessor."""
    
    def test_init_default(self):
        """Test processor initialization with defaults."""
        processor = DataProcessor("test")
        assert processor.name == "test"
        assert processor.config == {}
    
    def test_process_data(self):
        """Test data processing functionality."""
        processor = DataProcessor("test")
        result = processor.process(["a", "b"])
        assert result == ["processed_a", "processed_b"]
```

## Common Tasks

### Adding a New Feature

1. **Create feature branch**: `git checkout -b feature/feature-name`
2. **Add functionality**: Implement in appropriate module
3. **Add tests**: Write comprehensive tests
4. **Update documentation**: Add docstrings and examples
5. **Run quality checks**: `python scripts/run_tests.py --all`
6. **Commit and push**: Follow conventional commit format

### Adding a New Module

1. **Create module file**: `src/pyvabs/new_module.py`
2. **Add to __init__.py**: Export public functions/classes
3. **Create test file**: `tests/test_new_module.py`
4. **Add to documentation**: Update docs if needed
5. **Update __all__**: Add exports to package `__all__`

### Debugging Issues

```bash
# Run specific test with verbose output
uv run pytest tests/test_core.py::TestPyVabsCore::test_process -v -s

# Check import issues
uv run python -c "import pyvabs.core; print('OK')"

# Verify environment
uv run python -c "import sys; print(sys.path)"

# Check dependencies
uv tree
```

## Quality Standards

### Required Checks

- ✅ All tests pass: `uv run pytest`
- ✅ Code formatted: `uv run black --check src tests`
- ✅ Imports sorted: `uv run isort --check-only src tests`
- ✅ Linting clean: `uv run flake8 src tests`
- ✅ Type checking: `uv run mypy src`
- ✅ Coverage >90%: `uv run pytest --cov=src/pyvabs --cov-fail-under=90`

### Pre-commit Hooks

```bash
# Install hooks (done by setup script)
uv run pre-commit install

# Run hooks manually
uv run pre-commit run --all-files
```

## Troubleshooting

### Common Issues

**Import errors**:
```bash
# Use uv run for commands
uv run python script.py

# Or activate environment
source .venv/bin/activate  # Unix/macOS
.venv\Scripts\activate     # Windows
```

**Test failures**:
```bash
# Update dependencies
uv sync

# Clear cache
uv cache clean
```

**Type checking errors**:
```bash
# Check specific file
uv run mypy src/pyvabs/core.py

# Ignore specific error (last resort)
# type: ignore[error-code]
```

## Getting Help

- 📖 Read [DEVELOPER.md](../DEVELOPER.md) for detailed information
- 🏗️ Check [architecture.md](architecture.md) for design decisions
- 🐛 Look at existing tests for examples
- 💬 Ask questions in issues or discussions

## Next Steps

After setup, consider:

1. **Explore the codebase**: Read through `src/pyvabs/` modules
2. **Run examples**: Execute scripts in `examples/` directory
3. **Read tests**: Understand expected behavior from test cases
4. **Check CI**: Look at `.github/workflows/ci.yml` for full pipeline
5. **Contribute**: Pick an issue or suggest improvements

---

Happy coding! 🚀
