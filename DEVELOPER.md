# Developer Documentation

This document provides comprehensive information for developers working on the PyVabs project.

## Table of Contents

- [Project Architecture](#project-architecture)
- [Development Environment Setup](#development-environment-setup)
- [Development Workflow](#development-workflow)
- [Code Standards](#code-standards)
- [Testing Strategy](#testing-strategy)
- [Documentation](#documentation)
- [Release Process](#release-process)
- [Troubleshooting](#troubleshooting)

## Project Architecture

### Directory Structure

```
pyvabs/
├── src/pyvabs/              # Main package source code
│   ├── __init__.py          # Package initialization and public API
│   ├── core.py              # Core functionality (PyVabsCore class)
│   ├── utils.py             # Utility functions and helpers
│   └── py.typed             # PEP 561 type information marker
├── tests/                   # Test suite
│   ├── __init__.py          # Test package initialization
│   ├── test_core.py         # Tests for core functionality
│   ├── test_utils.py        # Tests for utility functions
│   └── test_init.py         # Tests for package initialization
├── docs/                    # Sphinx documentation
│   ├── conf.py              # Sphinx configuration
│   ├── index.md             # Main documentation page
│   └── installation.md      # Installation instructions
├── examples/                # Usage examples and demos
│   ├── basic_usage.py       # Basic functionality demonstration
│   └── config_example.py    # Configuration handling example
├── scripts/                 # Development and utility scripts
│   ├── setup_dev.py         # Development environment setup
│   └── run_tests.py         # Test runner with various options
├── .github/workflows/       # GitHub Actions CI/CD
│   └── ci.yml               # Continuous integration pipeline
├── pyproject.toml           # Project configuration (PEP 518)
├── .gitignore               # Git ignore patterns
├── .pre-commit-config.yaml  # Pre-commit hooks configuration
├── LICENSE                  # MIT License
├── README.md                # User-facing project documentation
├── CHANGELOG.md             # Version history and release notes
└── DEVELOPER.md             # This file - developer documentation
```

### Package Design

- **src/ layout**: Follows modern Python packaging best practices
- **Type hints**: Full type annotation coverage with mypy validation
- **Modular design**: Clear separation between core functionality and utilities
- **Public API**: Controlled exports through `__init__.py` `__all__`

## Development Environment Setup

### Prerequisites

- Python 3.13 or higher
- [uv](https://docs.astral.sh/uv/) package manager
- Git

### Quick Setup

1. **Clone and navigate to the project:**
   ```bash
   git clone <repository-url>
   cd pyvabs
   ```

2. **Run the automated setup script:**
   ```bash
   python scripts/setup_dev.py
   ```

3. **Or manually set up the environment:**
   ```bash
   # Install all dependencies including dev tools
   uv sync --extra dev --extra docs
   
   # Install pre-commit hooks
   uv run pre-commit install
   
   # Verify installation
   uv run pytest --version
   uv run black --version
   uv run mypy --version
   ```

### Development Dependencies

The project uses the following development tools:

- **pytest** (8.4.1+): Testing framework with coverage
- **pytest-cov** (6.2.1+): Coverage reporting
- **black** (23.0+): Code formatting
- **isort** (5.0+): Import sorting
- **flake8** (6.0+): Linting and style checking
- **mypy** (1.0+): Static type checking
- **pre-commit** (3.0+): Git hooks for code quality
- **sphinx** (7.0+): Documentation generation
- **sphinx-rtd-theme** (1.0+): Documentation theme
- **myst-parser** (2.0+): Markdown support for Sphinx

## Development Workflow

### Daily Development

1. **Start development session:**
   ```bash
   # Activate virtual environment (if not using uv run)
   source .venv/bin/activate  # Unix/macOS
   # or
   .venv\Scripts\activate     # Windows
   
   # Pull latest changes
   git pull origin main
   ```

2. **Make changes and test frequently:**
   ```bash
   # Run tests for specific module
   uv run pytest tests/test_core.py -v
   
   # Run all tests with coverage
   uv run pytest --cov=src/pyvabs
   
   # Quick format and lint check
   uv run black src tests
   uv run isort src tests
   uv run flake8 src tests
   ```

3. **Before committing:**
   ```bash
   # Run comprehensive checks
   python scripts/run_tests.py --all
   
   # Or manually:
   uv run pytest
   uv run black --check src tests
   uv run isort --check-only src tests
   uv run flake8 src tests
   uv run mypy src
   ```

### Branch Strategy

- **main**: Stable release branch
- **develop**: Integration branch for features
- **feature/***: Feature development branches
- **hotfix/***: Critical bug fixes

### Commit Guidelines

- Use conventional commit format: `type(scope): description`
- Types: `feat`, `fix`, `docs`, `style`, `refactor`, `test`, `chore`
- Keep commits atomic and focused
- Write clear, descriptive commit messages

## Code Standards

### Python Style

- **PEP 8** compliance enforced by flake8
- **Black** formatting with 88-character line length
- **isort** for import organization with black profile
- **Type hints** required for all public functions and methods

### Code Quality Metrics

- **Test coverage**: Maintain >90% coverage
- **Type coverage**: 100% type annotation coverage
- **Linting**: Zero flake8 violations
- **Documentation**: All public APIs must be documented

### Example Code Style

```python
"""Module docstring following Google style."""

from typing import Any, Dict, List, Optional


class ExampleClass:
    """Class docstring with clear description.
    
    Attributes:
        name: The name of the instance.
        config: Configuration dictionary.
    """
    
    def __init__(self, name: str, config: Optional[Dict[str, Any]] = None) -> None:
        """Initialize the instance.
        
        Args:
            name: The name for this instance.
            config: Optional configuration dictionary.
        """
        self.name = name
        self.config = config or {}
    
    def process_data(self, data: List[Any]) -> List[Any]:
        """Process input data.
        
        Args:
            data: List of data items to process.
            
        Returns:
            List of processed data items.
            
        Raises:
            ValueError: If data is empty.
        """
        if not data:
            raise ValueError("Data cannot be empty")
        return [f"processed_{item}" for item in data]
```

## Testing Strategy

### Test Organization

- **Unit tests**: Test individual functions and methods
- **Integration tests**: Test component interactions
- **Example tests**: Verify example scripts work correctly

### Test Naming Convention

- Test files: `test_<module_name>.py`
- Test classes: `Test<ClassName>`
- Test methods: `test_<functionality>_<condition>`

### Coverage Requirements

- **Minimum coverage**: 90%
- **Critical paths**: 100% coverage required
- **Documentation**: Coverage reports in HTML and XML formats

### Running Tests

```bash
# All tests with coverage
python scripts/run_tests.py --coverage

# Specific test file
python scripts/run_tests.py tests/test_core.py

# Fast tests (no coverage)
python scripts/run_tests.py --fast

# Verbose output
python scripts/run_tests.py --verbose

# All quality checks
python scripts/run_tests.py --all
```

## Documentation

### Documentation Structure

- **User documentation**: README.md, docs/ directory
- **Developer documentation**: This file (DEVELOPER.md)
- **API documentation**: Auto-generated from docstrings
- **Examples**: Practical usage examples in examples/

### Building Documentation

```bash
# Install documentation dependencies
uv sync --extra docs

# Build HTML documentation
cd docs
uv run sphinx-build -b html . _build/html

# View documentation
# Open docs/_build/html/index.html in browser
```

### Documentation Standards

- **Docstring format**: Google style
- **API documentation**: Auto-generated with Sphinx
- **Examples**: Working code examples with explanations
- **Markdown**: MyST parser for Markdown in Sphinx

## Release Process

### Version Management

- **Semantic versioning**: MAJOR.MINOR.PATCH
- **Version locations**: `pyproject.toml`, `src/pyvabs/__init__.py`
- **Changelog**: Update CHANGELOG.md for each release

### Release Checklist

1. **Pre-release checks:**
   ```bash
   python scripts/run_tests.py --all
   uv run pytest --cov=src/pyvabs --cov-fail-under=90
   ```

2. **Update version and changelog:**
   - Update version in `pyproject.toml`
   - Update `__version__` in `src/pyvabs/__init__.py`
   - Add entry to CHANGELOG.md

3. **Create release:**
   ```bash
   git tag v<version>
   git push origin v<version>
   ```

4. **Build and publish:**
   ```bash
   uv build
   uv publish
   ```

## Troubleshooting

### Common Issues

1. **Import errors**: Ensure you're using `uv run` or have activated the virtual environment
2. **Test failures**: Check if dependencies are up to date with `uv sync`
3. **Type checking errors**: Verify type hints are correct and complete
4. **Pre-commit failures**: Run tools manually to see detailed error messages

### Debug Commands

```bash
# Check environment
uv run python -c "import pyvabs; print(pyvabs.__version__)"

# Verify dependencies
uv tree

# Check virtual environment
uv run python -c "import sys; print(sys.executable)"

# Test specific functionality
uv run python -c "from pyvabs.core import PyVabsCore; print('Core import OK')"
```

### Getting Help

- Check existing issues in the repository
- Run tests with verbose output for detailed error information
- Verify your development environment matches the requirements
- Consult the CI configuration for the expected environment setup

---

This developer documentation should be updated as the project evolves. Keep it synchronized with actual project structure and practices.
