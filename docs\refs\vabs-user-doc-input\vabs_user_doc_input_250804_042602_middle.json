{"pdf_info": [{"para_blocks": [{"bbox": [83, 104, 540, 131], "type": "text", "lines": [{"bbox": [83, 104, 540, 131], "spans": [{"bbox": [83, 104, 540, 131], "type": "text", "content": "5. If analysis is equal to 20, VABS will carry out a dehomogenization analysis to recover 3D displacements, strains, stresses based on the linear beam theory with large outputs."}]}], "index": 0}, {"bbox": [83, 140, 541, 181], "type": "text", "lines": [{"bbox": [83, 140, 541, 181], "spans": [{"bbox": [83, 140, 541, 181], "type": "text", "content": "6. If analysis is equal to 3, VABS will carry out a dehomogenization analysis to evaluate the distribution of failure index and strength ratio over the cross-section, and the strength ratio of the entire cross-section."}]}], "index": 1}, {"bbox": [71, 190, 541, 231], "type": "text", "lines": [{"bbox": [71, 190, 541, 231], "spans": [{"bbox": [71, 190, 541, 231], "type": "text", "content": "If analysis is equal to 1, 2, 3, we can also provide another command line argument nload as the total number of loads for VABS to perform the corresponding dehomogenization analysis. If this argument does not exist, it will perform the dehomogenization analysis for a single load case."}]}], "index": 2}, {"bbox": [70, 248, 190, 265], "type": "title", "lines": [{"bbox": [70, 248, 190, 265], "spans": [{"bbox": [70, 248, 190, 265], "type": "text", "content": "7 VABS Inputs"}]}], "index": 3, "level": 1, "line_avg_height": 22}, {"bbox": [70, 275, 541, 316], "type": "text", "lines": [{"bbox": [70, 275, 541, 316], "spans": [{"bbox": [70, 275, 541, 316], "type": "text", "content": "Although a few preprocessors, such as PreVABS, have been developed to create VABS input files, it is still beneficial for advanced users, particularly those who want to embedded VABS in their own design environment, to understand the meaning of the input data."}]}], "index": 4}, {"bbox": [70, 330, 541, 425], "type": "text", "lines": [{"bbox": [70, 330, 541, 425], "spans": [{"bbox": [70, 330, 541, 425], "type": "text", "content": "Starting from VABS 4.0, the inputs for the VABS are separated into two files: homogenization input file and dehomogenization input file. VABS homogenization run only requires the homogenization input file with a name of the user's choice. The dehomogenization input file associated with the homogenization input file with extension glb. In other words, if the homogenization input file name is input_file_name, the dehomogenization input file must be input_file_name glb. Empty lines or comment lines can be used in the input file for readability. The comment line must start with \"!\""}]}], "index": 5}, {"bbox": [70, 440, 262, 454], "type": "title", "lines": [{"bbox": [70, 440, 262, 454], "spans": [{"bbox": [70, 440, 262, 454], "type": "text", "content": "7.1 Homogenization Input File"}]}], "index": 6, "level": 2, "line_avg_height": 19}, {"bbox": [70, 461, 541, 556], "type": "text", "lines": [{"bbox": [70, 461, 541, 556], "spans": [{"bbox": [70, 461, 541, 556], "type": "text", "content": "The first line lists two newly introduced integer flags arranged as: \"format_flag nlayer\". If the first flag is 1, the input is prepared in the new format, otherwise, it is prepared in the old format. The second integer provides the number of layers in the section. Note, here layer is defined as a unique combination of material type and layup orientation, it does not necessarily corresponds to the definition used in the manufacturing sense. For example, even if a section is made of a single isotropic material, we consider it has one layer. Hence, nlayer should be always given a value greater than 1 if format_flag=1 and it is not used when using the old format."}]}], "index": 7}, {"bbox": [70, 570, 541, 678], "type": "text", "lines": [{"bbox": [70, 570, 541, 678], "spans": [{"bbox": [70, 570, 541, 678], "type": "text", "content": "The second line has two flags arranged as: \"Timoshenko_flag damping_flag thermal_flag\". The first flag, Timoshenko_flag, can be only 1 or 0. If it is 1, VABS will construct both the classical model (also called the <PERSON><PERSON><PERSON><PERSON> <PERSON> model) and the <PERSON><PERSON><PERSON> model. If it is 0, it will only construct the classical model. The second flag, damping_flag, can be equal to 0 or 1. If it is equal to 0, VABS will not compute the damping matrix for the section. If it is equal to 1, VABS will compute the damping matrix. The third flag, thermal_flag, can be equal to 0 or 3. If it is equal to zero, VABS will carry out a pure mechanical analysis. If it is equal to 3, VABS will carry out a one- way coupled thermoelastic analysis."}]}], "index": 8}], "discarded_blocks": [], "page_size": [612, 792], "page_idx": 0}, {"para_blocks": [{"type": "image", "bbox": [69, 103, 505, 356], "blocks": [{"bbox": [69, 103, 505, 356], "lines": [{"bbox": [69, 103, 505, 356], "spans": [{"bbox": [69, 103, 505, 356], "type": "image", "image_path": "f412d7871b06261b1b1be61a4cd2b9a1bae678964ff5eeab0895486a331347f7.jpg"}]}], "index": 0, "type": "image_body"}], "index": 0}, {"bbox": [69, 402, 542, 430], "type": "text", "lines": [{"bbox": [69, 402, 542, 430], "spans": [{"bbox": [69, 402, 542, 430], "type": "text", "content": "The third line has four flags arranged as: “curve_flag oblique_flag trapeze_flag <PERSON><PERSON><PERSON>_flag.” These flags can be only 1 or 0. Their uses are explained in the following:"}]}], "index": 1}, {"bbox": [83, 438, 542, 465], "type": "text", "lines": [{"bbox": [83, 438, 542, 465], "spans": [{"bbox": [83, 438, 542, 465], "type": "text", "content": "1. To model initially curved and twisted beams, curve flag is 1, and three real numbers for the twist ( "}, {"bbox": [83, 438, 542, 465], "type": "inline_equation", "content": "k_{1}"}, {"bbox": [83, 438, 542, 465], "type": "text", "content": " ) and curvatures ( "}, {"bbox": [83, 438, 542, 465], "type": "inline_equation", "content": "k_{2}"}, {"bbox": [83, 438, 542, 465], "type": "text", "content": " and "}, {"bbox": [83, 438, 542, 465], "type": "inline_equation", "content": "k_{3}"}, {"bbox": [83, 438, 542, 465], "type": "text", "content": " ) should be provided in the very next line."}]}], "index": 2}, {"bbox": [83, 474, 542, 596], "type": "text", "lines": [{"bbox": [83, 474, 542, 596], "spans": [{"bbox": [83, 474, 542, 596], "type": "text", "content": "2. To model oblique cross-sections, oblique flag is 1, and two real numbers are needed in the following line to specify the orientation of an oblique reference cross-section, see Figure 6 for a sketch of such a cross-section. The first number is cosine of the angle between normal of the oblique section ( "}, {"bbox": [83, 474, 542, 596], "type": "inline_equation", "content": "y_{1}"}, {"bbox": [83, 474, 542, 596], "type": "text", "content": " ) and beam axis "}, {"bbox": [83, 474, 542, 596], "type": "inline_equation", "content": "x_{1}"}, {"bbox": [83, 474, 542, 596], "type": "text", "content": " . The second number is cosine of the angle between "}, {"bbox": [83, 474, 542, 596], "type": "inline_equation", "content": "y_{2}"}, {"bbox": [83, 474, 542, 596], "type": "text", "content": " of the oblique section and beam axis "}, {"bbox": [83, 474, 542, 596], "type": "inline_equation", "content": "\\left(x_{1}\\right)"}, {"bbox": [83, 474, 542, 596], "type": "text", "content": " . The summation of the square of these two numbers should not be greater than 1.0 in double precision. The inputs including coordinates, material properties, etc. and the outputs including mass matrix, stiffness matrix, etc. are given in the oblique system, the "}, {"bbox": [83, 474, 542, 596], "type": "inline_equation", "content": "y_{i}"}, {"bbox": [83, 474, 542, 596], "type": "text", "content": " coordinate system as shown in Figure 6. Note that this feature is only enabled for the classical beam model."}]}], "index": 3}, {"bbox": [83, 605, 315, 619], "type": "text", "lines": [{"bbox": [83, 605, 315, 619], "spans": [{"bbox": [83, 605, 315, 619], "type": "text", "content": "3. To obtain the trapeze effect, trapeze flag is 1."}]}], "index": 4}, {"bbox": [83, 628, 542, 695], "type": "text", "lines": [{"bbox": [83, 628, 542, 695], "spans": [{"bbox": [83, 628, 542, 695], "type": "text", "content": "4. To obtain the <PERSON><PERSON><PERSON> model, Vlasov flag is 1. Vlasov flag can be 1 only if <PERSON><PERSON><PERSON> flag is 1. VABS will first construct the <PERSON><PERSON><PERSON> model, which determines the location of the shear center. If the shear center is not at the origin of the beam coordinate system, VABS will move the origin of beam coordinate system to the shear center and repeat the calculation to obtain the <PERSON><PERSON><PERSON> model."}]}], "index": 5}], "discarded_blocks": [], "page_size": [612, 792], "page_idx": 1}, {"para_blocks": [{"bbox": [70, 104, 541, 131], "type": "text", "lines": [{"bbox": [70, 104, 541, 131], "spans": [{"bbox": [70, 104, 541, 131], "type": "text", "content": "The next line lists three integers arranged as: \"nnode nelem nmate,\" where nnode is the total number of nodes, nelem the total number of elements, and nmate the total number of material types."}]}], "index": 0}, {"bbox": [70, 145, 541, 199], "type": "text", "lines": [{"bbox": [70, 145, 541, 199], "spans": [{"bbox": [70, 145, 541, 199], "type": "text", "content": "The next nnode lines are the coordinates for each node arranged as: \"node_no "}, {"bbox": [70, 145, 541, 199], "type": "inline_equation", "content": "x_{2}"}, {"bbox": [70, 145, 541, 199], "type": "inline_equation", "content": "x_{3}"}, {"bbox": [70, 145, 541, 199], "type": "text", "content": " \" where node_no is an integer representing the unique number assigned to each node and "}, {"bbox": [70, 145, 541, 199], "type": "inline_equation", "content": "x_{2}"}, {"bbox": [70, 145, 541, 199], "type": "inline_equation", "content": "x_{3}"}, {"bbox": [70, 145, 541, 199], "type": "text", "content": " are two real numbers describing the location "}, {"bbox": [70, 145, 541, 199], "type": "inline_equation", "content": "(x_{2},x_{3})"}, {"bbox": [70, 145, 541, 199], "type": "text", "content": " of the node. Although the arrangement of node_no is not necessary to be consecutive, every node starting from 1 to nnode should be present."}]}], "index": 1}, {"bbox": [70, 213, 541, 308], "type": "text", "lines": [{"bbox": [70, 213, 541, 308], "spans": [{"bbox": [70, 213, 541, 308], "type": "text", "content": "The next nelem lines list 10 integers for the nodes for each element (also known as the connectivity relations). They are arranged as: \"elem_no node_1 node_2 node_3 node_4 node_5 node_6 node_7 node_8 node_9,\" where elem_no is the number of element and node_i "}, {"bbox": [70, 213, 541, 308], "type": "inline_equation", "content": "(i = 1,2,\\ldots ,9)"}, {"bbox": [70, 213, 541, 308], "type": "text", "content": " are nodes of this element. If a node is not present in the element, the value is 0. If node_4 is 0, the element is a triangular element; see Figures 2 and 3 for the VABS numbering convention. Although the arrangement of elem_no is not necessary to be consecutive, every element starting from 1 to nelem should be present."}]}], "index": 2}, {"bbox": [70, 321, 541, 430], "type": "text", "lines": [{"bbox": [70, 321, 541, 430], "spans": [{"bbox": [70, 321, 541, 430], "type": "text", "content": "If format "}, {"bbox": [70, 321, 541, 430], "type": "inline_equation", "content": "\\cdot f l a g = 1"}, {"bbox": [70, 321, 541, 430], "type": "text", "content": " , that is, if the new format is used, the next nelem lines list the layer type and the layer plane angle "}, {"bbox": [70, 321, 541, 430], "type": "inline_equation", "content": "\\left(\\theta_{1}\\right)"}, {"bbox": [70, 321, 541, 430], "type": "text", "content": " for each element as: elem_no layer_type "}, {"bbox": [70, 321, 541, 430], "type": "inline_equation", "content": "\\theta_{1}"}, {"bbox": [70, 321, 541, 430], "type": "text", "content": " , where layer_type is an integer representing which layer the element elem_no belongs to, and "}, {"bbox": [70, 321, 541, 430], "type": "inline_equation", "content": "\\theta_{1}"}, {"bbox": [70, 321, 541, 430], "type": "text", "content": " is a real number describing the layer plane angle for the element. Here, "}, {"bbox": [70, 321, 541, 430], "type": "inline_equation", "content": "\\theta_{1}"}, {"bbox": [70, 321, 541, 430], "type": "text", "content": " is assumed to be constant for each element, thus it can be calculated at any material point belonging to the element, such as the centroid, or computed as the average of "}, {"bbox": [70, 321, 541, 430], "type": "inline_equation", "content": "\\theta_{1}"}, {"bbox": [70, 321, 541, 430], "type": "text", "content": " of all the points within the element. Although the arrangement of elem_no is not necessary to be consecutive, every element starting from 1 to nelem should be present. For isotropic materials, "}, {"bbox": [70, 321, 541, 430], "type": "inline_equation", "content": "\\theta_{1}"}, {"bbox": [70, 321, 541, 430], "type": "text", "content": " will not enter the calculations."}]}], "index": 3}, {"bbox": [70, 443, 541, 592], "type": "text", "lines": [{"bbox": [70, 443, 541, 592], "spans": [{"bbox": [70, 443, 541, 592], "type": "text", "content": "If format "}, {"bbox": [70, 443, 541, 592], "type": "inline_equation", "content": "\\cdot f l a g = 1"}, {"bbox": [70, 443, 541, 592], "type": "text", "content": " , that is, if the old format is used, the next nelem lines list the material type and layup parameters for each element as: elem_no material_type "}, {"bbox": [70, 443, 541, 592], "type": "inline_equation", "content": "\\theta_{3}"}, {"bbox": [70, 443, 541, 592], "type": "inline_equation", "content": "\\theta_{1}(9)"}, {"bbox": [70, 443, 541, 592], "type": "text", "content": " , where material_type is an integer representing the type of the material for the element elem_no, "}, {"bbox": [70, 443, 541, 592], "type": "inline_equation", "content": "\\theta_{3}"}, {"bbox": [70, 443, 541, 592], "type": "text", "content": " is a real number representing the layup angle in degrees for this element, and "}, {"bbox": [70, 443, 541, 592], "type": "inline_equation", "content": "\\theta_{1}(9)"}, {"bbox": [70, 443, 541, 592], "type": "text", "content": " is an array storing nine real numbers for the layer plane angles at the nodes of this element. For simplification, if the ply orientation can be considered as uniform for this element, "}, {"bbox": [70, 443, 541, 592], "type": "inline_equation", "content": "\\theta_{1}(1)"}, {"bbox": [70, 443, 541, 592], "type": "text", "content": " stores the layer plane angle and "}, {"bbox": [70, 443, 541, 592], "type": "inline_equation", "content": "\\theta_{1}(2) = 540^{\\circ}"}, {"bbox": [70, 443, 541, 592], "type": "text", "content": " , and all the rest can be zeros or other real numbers because they do not enter the calculation. If the element has fewer than nine nodes, zeros are to be input for the corresponding missing nodes, as in the case for connectivity. Although the arrangement of elem_no is not necessary to be consecutive, every element starting from 1 to nelem should be present. For isotropic materials, neither "}, {"bbox": [70, 443, 541, 592], "type": "inline_equation", "content": "\\theta_{3}"}, {"bbox": [70, 443, 541, 592], "type": "text", "content": " nor "}, {"bbox": [70, 443, 541, 592], "type": "inline_equation", "content": "\\theta_{1}(9)"}, {"bbox": [70, 443, 541, 592], "type": "text", "content": " will enter the calculations."}]}], "index": 4}, {"bbox": [70, 605, 541, 700], "type": "text", "lines": [{"bbox": [70, 605, 541, 700], "spans": [{"bbox": [70, 605, 541, 700], "type": "text", "content": "If format "}, {"bbox": [70, 605, 541, 700], "type": "inline_equation", "content": "\\cdot f l a g = 1"}, {"bbox": [70, 605, 541, 700], "type": "text", "content": " , that is, if the new format is used, the next nlayer lines define the layers used in the section. They are arranged as: layer_id mate_type "}, {"bbox": [70, 605, 541, 700], "type": "inline_equation", "content": "\\theta_{3}"}, {"bbox": [70, 605, 541, 700], "type": "text", "content": " , where layer_id is an integer denoting the identification number for each layer, mate_type is an integer denoting the material type used in the layer, and "}, {"bbox": [70, 605, 541, 700], "type": "inline_equation", "content": "\\theta_{3}"}, {"bbox": [70, 605, 541, 700], "type": "text", "content": " is a real number denoting the layup orientation. For example, if layer 1 is made of material 1 and having "}, {"bbox": [70, 605, 541, 700], "type": "inline_equation", "content": "- 15^{\\circ}"}, {"bbox": [70, 605, 541, 700], "type": "text", "content": " layup, we will provide the information as 1 1  15.0. If damping "}, {"bbox": [70, 605, 541, 700], "type": "inline_equation", "content": "\\cdot f l a g"}, {"bbox": [70, 605, 541, 700], "type": "text", "content": " is 1, a damping coefficient for each layer is also needed to input right after "}, {"bbox": [70, 605, 541, 700], "type": "inline_equation", "content": "\\theta_{3}"}, {"bbox": [70, 605, 541, 700], "type": "text", "content": " . In others words, the input should be arranged as layer id mate_type "}, {"bbox": [70, 605, 541, 700], "type": "inline_equation", "content": "\\theta_{3}"}, {"bbox": [70, 605, 541, 700], "type": "text", "content": " damping_layer, with damping_layer indicating"}]}], "index": 5}], "discarded_blocks": [], "page_size": [612, 792], "page_idx": 2}, {"para_blocks": [{"bbox": [70, 104, 247, 118], "type": "text", "lines": [{"bbox": [70, 104, 247, 118], "spans": [{"bbox": [70, 104, 247, 118], "type": "text", "content": "the damping coefficient for the layer."}]}], "index": 0}, {"bbox": [86, 131, 460, 145], "type": "text", "lines": [{"bbox": [86, 131, 460, 145], "spans": [{"bbox": [86, 131, 460, 145], "type": "text", "content": "The next nmate blocks defines the material properties. They are arranged as:"}]}], "index": 1}, {"bbox": [88, 147, 145, 158], "type": "text", "lines": [{"bbox": [88, 147, 145, 158], "spans": [{"bbox": [88, 147, 145, 158], "type": "text", "content": "mat id orth"}]}], "index": 2}, {"bbox": [88, 159, 170, 171], "type": "text", "lines": [{"bbox": [88, 159, 170, 171], "spans": [{"bbox": [88, 159, 170, 171], "type": "text", "content": "const1 const2 ...."}]}], "index": 3}, {"bbox": [70, 173, 541, 200], "type": "text", "lines": [{"bbox": [70, 173, 541, 200], "spans": [{"bbox": [70, 173, 541, 200], "type": "text", "content": "where mat id is the number of material type, orth is the flag to indicate whether the material is isotropic (0), orthotropic (1) or general anisotropic (2). The rest are material constants."}]}], "index": 4}, {"bbox": [86, 213, 506, 227], "type": "text", "lines": [{"bbox": [86, 213, 506, 227], "spans": [{"bbox": [86, 213, 506, 227], "type": "text", "content": "For isotropic materials, orth is 0, if thermal_flag is 0, there are 3 constants arranged as:"}]}], "index": 5}, {"bbox": [88, 228, 115, 239], "type": "text", "lines": [{"bbox": [88, 228, 115, 239], "spans": [{"bbox": [88, 228, 115, 239], "type": "inline_equation", "content": "E"}, {"bbox": [88, 228, 115, 239], "type": "inline_equation", "content": "\\nu"}]}], "index": 6}, {"bbox": [88, 242, 95, 252], "type": "text", "lines": [{"bbox": [88, 242, 95, 252], "spans": [{"bbox": [88, 242, 95, 252], "type": "inline_equation", "content": "\\rho"}]}], "index": 7}, {"bbox": [70, 254, 541, 294], "type": "text", "lines": [{"bbox": [70, 254, 541, 294], "spans": [{"bbox": [70, 254, 541, 294], "type": "text", "content": "where "}, {"bbox": [70, 254, 541, 294], "type": "inline_equation", "content": "E"}, {"bbox": [70, 254, 541, 294], "type": "text", "content": " is the <PERSON>'s modulus, "}, {"bbox": [70, 254, 541, 294], "type": "inline_equation", "content": "\\nu"}, {"bbox": [70, 254, 541, 294], "type": "text", "content": " is the Poisson's ratio, and "}, {"bbox": [70, 254, 541, 294], "type": "inline_equation", "content": "\\rho"}, {"bbox": [70, 254, 541, 294], "type": "text", "content": " is the density of the material. Poisson's ratio must be greater than - 1.0 and less than 0.5 for isotropic materials, although VABS allows users to input values that are very close to those limits."}]}], "index": 8}, {"bbox": [86, 308, 432, 321], "type": "text", "lines": [{"bbox": [86, 308, 432, 321], "spans": [{"bbox": [86, 308, 432, 321], "type": "text", "content": "If thermal_flag is 3 and orth is 0, and there are 4 constants arranged as:"}]}], "index": 9}, {"bbox": [88, 323, 115, 334], "type": "text", "lines": [{"bbox": [88, 323, 115, 334], "spans": [{"bbox": [88, 323, 115, 334], "type": "inline_equation", "content": "E"}, {"bbox": [88, 323, 115, 334], "type": "inline_equation", "content": "\\nu"}]}], "index": 10}, {"bbox": [88, 338, 95, 349], "type": "text", "lines": [{"bbox": [88, 338, 95, 349], "spans": [{"bbox": [88, 338, 95, 349], "type": "inline_equation", "content": "\\rho"}]}], "index": 11}, {"bbox": [88, 350, 95, 361], "type": "text", "lines": [{"bbox": [88, 350, 95, 361], "spans": [{"bbox": [88, 350, 95, 361], "type": "inline_equation", "content": "\\alpha"}]}], "index": 12}, {"bbox": [70, 362, 333, 376], "type": "text", "lines": [{"bbox": [70, 362, 333, 376], "spans": [{"bbox": [70, 362, 333, 376], "type": "text", "content": "where "}, {"bbox": [70, 362, 333, 376], "type": "inline_equation", "content": "\\alpha"}, {"bbox": [70, 362, 333, 376], "type": "text", "content": " is the coefficient of thermal expansion (CTE)."}]}], "index": 13}, {"bbox": [86, 388, 523, 403], "type": "text", "lines": [{"bbox": [86, 388, 523, 403], "spans": [{"bbox": [86, 388, 523, 403], "type": "text", "content": "For orthotropic materials, orth is 1, if thermal_flag is 0, there are 10 constants arranged as:"}]}], "index": 14}, {"bbox": [88, 404, 145, 416], "type": "text", "lines": [{"bbox": [88, 404, 145, 416], "spans": [{"bbox": [88, 404, 145, 416], "type": "inline_equation", "content": "E_{1}"}, {"bbox": [88, 404, 145, 416], "type": "inline_equation", "content": "E_{2}"}, {"bbox": [88, 404, 145, 416], "type": "inline_equation", "content": "E_{3}"}]}], "index": 15}, {"bbox": [88, 418, 156, 430], "type": "text", "lines": [{"bbox": [88, 418, 156, 430], "spans": [{"bbox": [88, 418, 156, 430], "type": "inline_equation", "content": "G_{12}"}, {"bbox": [88, 418, 156, 430], "type": "inline_equation", "content": "G_{13}"}, {"bbox": [88, 418, 156, 430], "type": "inline_equation", "content": "G_{23}"}]}], "index": 16}, {"bbox": [88, 434, 146, 444], "type": "text", "lines": [{"bbox": [88, 434, 146, 444], "spans": [{"bbox": [88, 434, 146, 444], "type": "inline_equation", "content": "\\nu_{12}"}, {"bbox": [88, 434, 146, 444], "type": "inline_equation", "content": "\\nu_{13}"}, {"bbox": [88, 434, 146, 444], "type": "inline_equation", "content": "\\nu_{23}"}]}], "index": 17}, {"bbox": [70, 446, 94, 456], "type": "text", "lines": [{"bbox": [70, 446, 94, 456], "spans": [{"bbox": [70, 446, 94, 456], "type": "inline_equation", "content": "\\rho"}]}], "index": 18}, {"bbox": [70, 456, 541, 498], "type": "text", "lines": [{"bbox": [70, 456, 541, 498], "spans": [{"bbox": [70, 456, 541, 498], "type": "text", "content": "including the Young's moduli "}, {"bbox": [70, 456, 541, 498], "type": "inline_equation", "content": "(E_{1},E_{2}"}, {"bbox": [70, 456, 541, 498], "type": "text", "content": " , and "}, {"bbox": [70, 456, 541, 498], "type": "inline_equation", "content": "E_{3}"}, {"bbox": [70, 456, 541, 498], "type": "text", "content": " ), the shear moduli "}, {"bbox": [70, 456, 541, 498], "type": "inline_equation", "content": "(G_{12},G_{13}"}, {"bbox": [70, 456, 541, 498], "type": "text", "content": " , and "}, {"bbox": [70, 456, 541, 498], "type": "inline_equation", "content": "G_{23}"}, {"bbox": [70, 456, 541, 498], "type": "text", "content": " ), the Poisson's ratios "}, {"bbox": [70, 456, 541, 498], "type": "inline_equation", "content": "(\\nu_{12},\\nu_{13}"}, {"bbox": [70, 456, 541, 498], "type": "text", "content": " , and "}, {"bbox": [70, 456, 541, 498], "type": "inline_equation", "content": "\\nu_{23}"}, {"bbox": [70, 456, 541, 498], "type": "text", "content": " ), and the mass density "}, {"bbox": [70, 456, 541, 498], "type": "inline_equation", "content": "(\\rho)"}, {"bbox": [70, 456, 541, 498], "type": "text", "content": " . The convention of values is such that these values will be used to form the following the <PERSON><PERSON>'s law for composite materials:"}]}], "index": 19}, {"bbox": [167, 503, 443, 594], "type": "interline_equation", "lines": [{"bbox": [167, 503, 443, 594], "spans": [{"bbox": [167, 503, 443, 594], "type": "interline_equation", "content": "\\left\\{\\begin{array}{l}{{\\epsilon_{11}}}\\\\ {{2\\epsilon_{12}}}\\\\ {{2\\epsilon_{13}}}\\\\ {{\\epsilon_{22}}}\\\\ {{2\\epsilon_{23}}}\\\\ {{\\epsilon_{33}}}\\end{array}\\right\\}=\\left[\\begin{array}{c c c c c c c}{{\\frac{1}{E_{1}}}}&{{0}}&{{0}}&{{-\\frac{\\nu_{12}}{E_{1}}}}&{{0}}&{{-\\frac{\\nu_{13}}{E_{1}}}}\\\\ {{0}}&{{\\frac{1}{G_{12}}}}&{{0}}&{{0}}&{{0}}&{{0}}\\\\ {{0}}&{{0}}&{{\\frac{1}{G_{13}}}}&{{0}}&{{0}}&{{0}}\\\\ {{-\\frac{\\nu_{12}}{E_{1}}}}&{{0}}&{{0}}&{{\\frac{1}{E_{2}}}}&{{0}}&{{-\\frac{\\nu_{23}}{E_{2}}}}\\\\ {{0}}&{{0}}&{{0}}&{{0}}&{{\\frac{1}{G_{23}}}}&{{0}}\\\\ {{-\\frac{\\nu_{13}}{E_{1}}}}&{{0}}&{{0}}&{{-\\frac{\\nu_{23}}{E_{2}}}}&{{0}}&{{\\frac{1}{E_{3}}}}\\end{array}\\right]\\left\\{\\begin{array}{l}{{\\sigma_{11}}}\\\\ {{\\sigma_{12}}}\\\\ {{\\sigma_{13}}}\\\\ {{\\sigma_{22}}}\\\\ {{\\sigma_{23}}}\\\\ {{\\sigma_{33}}}\\end{array}\\right\\}", "image_path": "2364d486f7642b40bbf31bec73b52ca8917264227c6b9e7fb973ff6a08d55a3b.jpg"}]}], "index": 20}, {"bbox": [69, 597, 420, 610], "type": "text", "lines": [{"bbox": [69, 597, 420, 610], "spans": [{"bbox": [69, 597, 420, 610], "type": "text", "content": "If thermal_flag is 3 and orth is 1, and there are 13 constants arranged as:"}]}], "index": 21}, {"bbox": [87, 612, 154, 624], "type": "text", "lines": [{"bbox": [87, 612, 154, 624], "spans": [{"bbox": [87, 612, 154, 624], "type": "inline_equation", "content": "E_{1}"}, {"bbox": [87, 612, 154, 624], "type": "inline_equation", "content": "E_{2}"}, {"bbox": [87, 612, 154, 624], "type": "inline_equation", "content": "E_{3}"}]}], "index": 22}, {"bbox": [88, 625, 154, 637], "type": "text", "lines": [{"bbox": [88, 625, 154, 637], "spans": [{"bbox": [88, 625, 154, 637], "type": "inline_equation", "content": "G_{12}"}, {"bbox": [88, 625, 154, 637], "type": "inline_equation", "content": "G_{13}"}, {"bbox": [88, 625, 154, 637], "type": "inline_equation", "content": "G_{23}"}]}], "index": 23}, {"bbox": [88, 640, 146, 651], "type": "text", "lines": [{"bbox": [88, 640, 146, 651], "spans": [{"bbox": [88, 640, 146, 651], "type": "inline_equation", "content": "\\nu_{12}"}, {"bbox": [88, 640, 146, 651], "type": "inline_equation", "content": "\\nu_{13}"}, {"bbox": [88, 640, 146, 651], "type": "inline_equation", "content": "\\nu_{23}"}]}], "index": 24}, {"bbox": [88, 654, 94, 664], "type": "text", "lines": [{"bbox": [88, 654, 94, 664], "spans": [{"bbox": [88, 654, 94, 664], "type": "inline_equation", "content": "\\rho"}]}], "index": 25}, {"bbox": [88, 667, 151, 677], "type": "text", "lines": [{"bbox": [88, 667, 151, 677], "spans": [{"bbox": [88, 667, 151, 677], "type": "inline_equation", "content": "\\alpha_{11}"}, {"bbox": [88, 667, 151, 677], "type": "inline_equation", "content": "\\alpha_{22}"}, {"bbox": [88, 667, 151, 677], "type": "inline_equation", "content": "\\alpha_{33}"}]}], "index": 26}, {"bbox": [69, 678, 339, 692], "type": "text", "lines": [{"bbox": [69, 678, 339, 692], "spans": [{"bbox": [69, 678, 339, 692], "type": "text", "content": "where "}, {"bbox": [69, 678, 339, 692], "type": "inline_equation", "content": "\\alpha_{11},\\alpha_{22},\\alpha_{33}"}, {"bbox": [69, 678, 339, 692], "type": "text", "content": " are the CTEs along three directions."}]}], "index": 27}], "discarded_blocks": [], "page_size": [612, 792], "page_idx": 3}, {"para_blocks": [{"bbox": [69, 104, 541, 131], "type": "text", "lines": [{"bbox": [69, 104, 541, 131], "spans": [{"bbox": [69, 104, 541, 131], "type": "text", "content": "For general anisotropic materials, orth is 2, if thermal_flag is 0, there are 22 constants arranged as:"}]}], "index": 0}, {"bbox": [87, 134, 209, 226], "type": "interline_equation", "lines": [{"bbox": [87, 134, 209, 226], "spans": [{"bbox": [87, 134, 209, 226], "type": "interline_equation", "content": "\\begin{array}{c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c c} & c_{11} & c_{12} & c_{13} & c_{14} & c_{15} & c_{16} & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & \\\\ & c_{22} & c_{23} & c_{24} & c_{25} & c_{26} & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & \\\\ & & c_{33} & c_{34} & c_{35} & c_{36} & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & & \\end{array}", "image_path": "938a782cb7c3995ce24e2fba48b14d11962ae65ccb2f94deca3ed343a601bded.jpg"}]}], "index": 1}, {"bbox": [70, 227, 345, 240], "type": "text", "lines": [{"bbox": [70, 227, 345, 240], "spans": [{"bbox": [70, 227, 345, 240], "type": "text", "content": "These values are defined using the following <PERSON><PERSON>'s law:"}]}], "index": 2}, {"bbox": [186, 249, 425, 333], "type": "interline_equation", "lines": [{"bbox": [186, 249, 425, 333], "spans": [{"bbox": [186, 249, 425, 333], "type": "interline_equation", "content": "\\left\\{ \\begin{array}{l}{\\sigma_{11}}\\\\ {\\sigma_{12}}\\\\ {\\sigma_{13}}\\\\ {\\sigma_{22}}\\\\ {\\sigma_{23}}\\\\ {\\sigma_{33}} \\end{array} \\right\\} = \\left[ \\begin{array}{l l l l l l l}{c_{11}} & {c_{12}} & {c_{13}} & {c_{14}} & {c_{15}} & {c_{16}}\\\\ {c_{12}} & {c_{22}} & {c_{23}} & {c_{24}} & {c_{25}} & {c_{26}}\\\\ {c_{13}} & {c_{23}} & {c_{33}} & {c_{34}} & {c_{35}} & {c_{36}}\\\\ {c_{14}} & {c_{24}} & {c_{34}} & {c_{44}} & {c_{45}} & {c_{46}}\\\\ {c_{15}} & {c_{25}} & {c_{35}} & {c_{45}} & {c_{55}} & {c_{56}}\\\\ {c_{16}} & {c_{26}} & {c_{36}} & {c_{46}} & {c_{56}} & {c_{66}} \\end{array} \\right]\\left\\{ \\begin{array}{l}{\\epsilon_{11}}\\\\ {2\\epsilon_{12}}\\\\ {2\\epsilon_{13}}\\\\ {\\epsilon_{22}}\\\\ {2\\epsilon_{23}}\\\\ {\\epsilon_{33}} \\end{array} \\right\\}", "image_path": "5f1699c80971f0b938838234af9f651847ef2d8c8d481d5c84eb2c921137217c.jpg"}]}], "index": 3}, {"bbox": [86, 339, 416, 353], "type": "text", "lines": [{"bbox": [86, 339, 416, 353], "spans": [{"bbox": [86, 339, 416, 353], "type": "text", "content": "If thermal_flag is 3 and orth is 2, there are 28 constants arranged as:"}]}], "index": 4}, {"bbox": [87, 356, 209, 449], "type": "interline_equation", "lines": [{"bbox": [87, 356, 209, 449], "spans": [{"bbox": [87, 356, 209, 449], "type": "interline_equation", "content": "\\begin{array}{r l}{c_{11}} & {c_{12} c_{13} c_{14} c_{15} c_{16}}\\\\ {c_{22} c_{23} c_{24} c_{25} c_{26}}\\\\ {c_{33} c_{34} c_{35} c_{36}}\\\\ {c_{44} c_{45} c_{46}}\\\\ {c_{55} c_{56}}\\\\ {c_{66}}\\\\ {\\rho} \\end{array}", "image_path": "412ecb19c3b5bc15a7cc923ebfaaf0428817178f59e012d8166fa61823f829c0.jpg"}]}], "index": 5}, {"bbox": [70, 449, 542, 543], "type": "text", "lines": [{"bbox": [70, 449, 542, 543], "spans": [{"bbox": [70, 449, 542, 543], "type": "inline_equation", "content": "\\alpha_{11} 2\\alpha_{12} 2\\alpha_{13} \\alpha_{22} 2\\alpha_{23} \\alpha_{33}"}, {"bbox": [70, 449, 542, 543], "type": "text", "content": " where "}, {"bbox": [70, 449, 542, 543], "type": "inline_equation", "content": "\\alpha_{ij}"}, {"bbox": [70, 449, 542, 543], "type": "text", "content": ", with "}, {"bbox": [70, 449, 542, 543], "type": "inline_equation", "content": "i = 1,2,3"}, {"bbox": [70, 449, 542, 543], "type": "text", "content": " and "}, {"bbox": [70, 449, 542, 543], "type": "inline_equation", "content": "j = 1,2,3"}, {"bbox": [70, 449, 542, 543], "type": "text", "content": ", are the components of the second- order CTE tensor. CTEs corresponding to the shear strains are multiplied by two because the engineering shear strains are twice of the corresponding tensorial shear strains. The material constants are expressed in the material coordinate system (see Figure 4). If the material properties are given in a different coordinate system, or the arrangement of stresses and strains are different from what VABS uses, a proper transformation of the material properties is needed."}]}], "index": 6}, {"bbox": [70, 555, 541, 597], "type": "text", "lines": [{"bbox": [70, 555, 541, 597], "spans": [{"bbox": [70, 555, 541, 597], "type": "text", "content": "If damping_flag is 1, a damping coefficient is input on the very next line following the density input. For example, if orth=0 and thermal_flag=3 (thermoelastic analysis with isotropic materials), the material constants are arranged as:"}]}], "index": 7}, {"bbox": [86, 598, 114, 650], "type": "interline_equation", "lines": [{"bbox": [86, 598, 114, 650], "spans": [{"bbox": [86, 598, 114, 650], "type": "interline_equation", "content": "\\begin{array}{c}{{E}}\\\\ {{\\rho}}\\\\ {{\\gamma}}\\\\ {{\\alpha}}\\end{array}", "image_path": "64bfc17737975e922b08c2d6c6f1ef161cee9fa3719484283925c291002fcfe1.jpg"}]}], "index": 8}, {"bbox": [70, 651, 541, 692], "type": "text", "lines": [{"bbox": [70, 651, 541, 692], "spans": [{"bbox": [70, 651, 541, 692], "type": "text", "content": "where "}, {"bbox": [70, 651, 541, 692], "type": "inline_equation", "content": "\\gamma"}, {"bbox": [70, 651, 541, 692], "type": "text", "content": " is a scalar representing the material damping property. It is noted that the damping coefficients for each layer and for each material are additive. In other words, the total damping coefficient used to scale the stiffness- related matrices is damping_layer + "}, {"bbox": [70, 651, 541, 692], "type": "inline_equation", "content": "\\gamma"}, {"bbox": [70, 651, 541, 692], "type": "text", "content": "."}]}], "index": 9}], "discarded_blocks": [], "page_size": [612, 792], "page_idx": 4}, {"para_blocks": [{"bbox": [70, 104, 541, 199], "type": "text", "lines": [{"bbox": [70, 104, 541, 199], "spans": [{"bbox": [70, 104, 541, 199], "type": "text", "content": "If thermal_flag is equal to 3, we also need to provide the following nnode lines for temperature for each node arranged as: \"node_no "}, {"bbox": [70, 104, 541, 199], "type": "inline_equation", "content": "T"}, {"bbox": [70, 104, 541, 199], "type": "text", "content": " \" where node_no is an integer representing the unique number assigned to each node and "}, {"bbox": [70, 104, 541, 199], "type": "inline_equation", "content": "T"}, {"bbox": [70, 104, 541, 199], "type": "text", "content": " is a real number describing the temperature of the node. These temperature values can be calculated either from a 3D heat conduction analysis or using VABS Conduction, which is a generalization of the VABS approach for heat conduction analysis. Although the arrangement of node_no is not necessary to be consecutive, every node starting from 1 to nnode should be present."}]}], "index": 0}, {"bbox": [70, 213, 541, 253], "type": "text", "lines": [{"bbox": [70, 213, 541, 253], "spans": [{"bbox": [70, 213, 541, 253], "type": "text", "content": "Now, we have prepared all the inputs necessary for performing the homogenization run to compute the inertial properties and structural properties of the cross- section. That is, when analysis in Section 6 does not exist."}]}], "index": 1}, {"bbox": [70, 268, 276, 283], "type": "title", "lines": [{"bbox": [70, 268, 276, 283], "spans": [{"bbox": [70, 268, 276, 283], "type": "text", "content": "7.2 Dehomogenization Input File"}]}], "index": 2, "level": 2, "line_avg_height": 19}, {"bbox": [70, 289, 541, 330], "type": "text", "lines": [{"bbox": [70, 289, 541, 330], "spans": [{"bbox": [70, 289, 541, 330], "type": "text", "content": "If analysis in Section 6 is equal to 1,2, 3, users should provide additional information in the dehomogenization input file for VABS to perform a dehomogenization analysis. A corresponding homogenization analysis must be run before carrying out the dehomogenization analysis."}]}], "index": 3}, {"bbox": [70, 344, 541, 398], "type": "text", "lines": [{"bbox": [70, 344, 541, 398], "spans": [{"bbox": [70, 344, 541, 398], "type": "text", "content": "If analysis is equal to 3, VABS will perform failure analysis of the cross- section. Strength properties for each material must be provided in the dehomogenization input file. Strength properties for each material include a failure criterion and corresponding strength constants. Two lines will be inserted and the inputs needed for failure analyses should be arranged as:"}]}], "index": 4}, {"bbox": [70, 399, 253, 426], "type": "text", "lines": [{"bbox": [70, 399, 253, 426], "spans": [{"bbox": [70, 399, 253, 426], "type": "text", "content": "failure_criterion num_of_constants const1 const2 const3"}]}], "index": 5}, {"bbox": [70, 426, 542, 493], "type": "text", "lines": [{"bbox": [70, 426, 542, 493], "spans": [{"bbox": [70, 426, 542, 493], "type": "text", "content": "failure_criterion is an integer identifier for the failure criterion. num_of_constants indicates the number of strength constants needed for the corresponding failure criterion. const1 const2 const3 are the corresponding strength constants. It is noted that this block of data should be corresponding to the material block in the homogenization input file. In other words, for each material with mat_id, we need to provide such information."}]}], "index": 6}, {"bbox": [70, 506, 541, 615], "type": "text", "lines": [{"bbox": [70, 506, 541, 615], "spans": [{"bbox": [70, 506, 541, 615], "type": "text", "content": "failure_criterion can be equal to 1, 2, 3, 4, 5, and another number greater than 10. For isotropic material, 1 is max principal stress criterion, 2 is max principal strain criterion, 3 is max shear stress criterion (also commonly called Tresca criterion), 4 max shear strain criterion, and 5 is <PERSON><PERSON> criterion. For anisotropic materials, 1 is max stress criterion for anisotropic materials, 2 is max strain criterion for anisotropic materials, 3 is Tsai- Hill criterion, 4 is Tsai- Wu criterion and 5 is <PERSON><PERSON> criterion. 11 and larger indicates a user- defined failure criterion. It is assumed that the number of strength constants will not be greater than 9 for a material. If the material is isotropic, the failure criterion and corresponding strength constants are defined as follows:"}]}], "index": 7}, {"bbox": [86, 624, 541, 701], "type": "text", "lines": [{"bbox": [86, 624, 541, 701], "spans": [{"bbox": [86, 624, 541, 701], "type": "text", "content": "If failure_criterion is 1, the max principal stress criterion is used and two strength constants are needed: one for tensile strength "}, {"bbox": [86, 624, 541, 701], "type": "inline_equation", "content": "(X)"}, {"bbox": [86, 624, 541, 701], "type": "text", "content": " and one for compressive strength "}, {"bbox": [86, 624, 541, 701], "type": "inline_equation", "content": "(X^{\\prime})"}, {"bbox": [86, 624, 541, 701], "type": "text", "content": " , arranged as "}, {"bbox": [86, 624, 541, 701], "type": "inline_equation", "content": "X,X^{\\prime}"}, {"bbox": [86, 624, 541, 701], "type": "text", "content": " . If failure_criterion is 2, the max principal strain criterion is used and two strength constants in terms of strains are needed: one for tensile strength "}, {"bbox": [86, 624, 541, 701], "type": "inline_equation", "content": "(X_{\\epsilon})"}, {"bbox": [86, 624, 541, 701], "type": "text", "content": " and one for compressive strength"}]}], "index": 8}], "discarded_blocks": [], "page_size": [612, 792], "page_idx": 5}, {"para_blocks": [{"bbox": [97, 104, 222, 118], "type": "text", "lines": [{"bbox": [97, 104, 222, 118], "spans": [{"bbox": [97, 104, 222, 118], "type": "inline_equation", "content": "(X_{\\epsilon}^{\\prime})"}, {"bbox": [97, 104, 222, 118], "type": "text", "content": " , arranged as "}, {"bbox": [97, 104, 222, 118], "type": "inline_equation", "content": "X_{\\epsilon},X_{\\epsilon}^{\\prime}"}]}], "index": 0}, {"bbox": [86, 127, 542, 155], "type": "text", "lines": [{"bbox": [86, 127, 542, 155], "spans": [{"bbox": [86, 127, 542, 155], "type": "text", "content": ". If failure_criterion is 3, the max shear stress criterion (aka Tresca criterion) is used and one shear strength constant ( "}, {"bbox": [86, 127, 542, 155], "type": "inline_equation", "content": "S"}, {"bbox": [86, 127, 542, 155], "type": "text", "content": " ) is needed."}]}], "index": 1}, {"bbox": [86, 163, 541, 191], "type": "text", "lines": [{"bbox": [86, 163, 541, 191], "spans": [{"bbox": [86, 163, 541, 191], "type": "text", "content": "If failure_criterion is 4, the max shear strain criterion is used and one shear strength constant in terms of strains "}, {"bbox": [86, 163, 541, 191], "type": "inline_equation", "content": "(S_{\\epsilon})"}, {"bbox": [86, 163, 541, 191], "type": "text", "content": " is needed."}]}], "index": 2}, {"bbox": [86, 199, 541, 213], "type": "text", "lines": [{"bbox": [86, 199, 541, 213], "spans": [{"bbox": [86, 199, 541, 213], "type": "text", "content": "If failure_criterion is 5, the <PERSON><PERSON> criterion is used and one strength constant "}, {"bbox": [86, 199, 541, 213], "type": "inline_equation", "content": "(X)"}, {"bbox": [86, 199, 541, 213], "type": "text", "content": " is needed."}]}], "index": 3}, {"bbox": [70, 221, 541, 249], "type": "text", "lines": [{"bbox": [70, 221, 541, 249], "spans": [{"bbox": [70, 221, 541, 249], "type": "text", "content": "If the material is not isotropic (transversely isotropic, orthotropic, or general anisotropic), the failure criteria and corresponding strength constants are defined as follows:"}]}], "index": 4}, {"bbox": [86, 258, 542, 312], "type": "text", "lines": [{"bbox": [86, 258, 542, 312], "spans": [{"bbox": [86, 258, 542, 312], "type": "text", "content": "If failure_criterion is 1, the max stress criterion is used and nine strength constants are needed: tensile strengths "}, {"bbox": [86, 258, 542, 312], "type": "inline_equation", "content": "(X,Y,Z)"}, {"bbox": [86, 258, 542, 312], "type": "text", "content": " in three directions, compressive strengths "}, {"bbox": [86, 258, 542, 312], "type": "inline_equation", "content": "(X^{\\prime},Y^{\\prime},Z^{\\prime})"}, {"bbox": [86, 258, 542, 312], "type": "text", "content": " in three directions, and shear strengths "}, {"bbox": [86, 258, 542, 312], "type": "inline_equation", "content": "(R,T,S)"}, {"bbox": [86, 258, 542, 312], "type": "text", "content": " in three principal planes, arranged as "}, {"bbox": [86, 258, 542, 312], "type": "inline_equation", "content": "X,Y,Z,<PERSON>^{\\prime},Y^{\\prime},Z^{\\prime},R,T,S"}]}], "index": 5}, {"bbox": [86, 321, 541, 376], "type": "text", "lines": [{"bbox": [86, 321, 541, 376], "spans": [{"bbox": [86, 321, 541, 376], "type": "text", "content": "If failure_criterion is 2, the max strain criterion is used and nine strength constants in terms of strains are needed: tensile strengths "}, {"bbox": [86, 321, 541, 376], "type": "inline_equation", "content": "(X_{\\epsilon},Y_{\\epsilon},Z_{\\epsilon})"}, {"bbox": [86, 321, 541, 376], "type": "text", "content": " in three directions, compressive strengths "}, {"bbox": [86, 321, 541, 376], "type": "inline_equation", "content": "(X_{\\epsilon}^{\\prime},Y_{\\epsilon}^{\\prime},Z_{\\epsilon}^{\\prime})"}, {"bbox": [86, 321, 541, 376], "type": "text", "content": " in three directions, and shear strengths "}, {"bbox": [86, 321, 541, 376], "type": "inline_equation", "content": "(R_{\\epsilon},T_{\\epsilon},S_{\\epsilon})"}, {"bbox": [86, 321, 541, 376], "type": "text", "content": " in three principal planes, arranged as "}, {"bbox": [86, 321, 541, 376], "type": "inline_equation", "content": "X_{\\epsilon},Y_{\\epsilon},Z_{\\epsilon},X_{\\epsilon}^{\\prime},Y_{\\epsilon}^{\\prime},Z_{\\epsilon}^{\\prime},R_{\\epsilon},T_{\\epsilon},S_{\\epsilon}"}]}], "index": 6}, {"bbox": [86, 384, 541, 425], "type": "text", "lines": [{"bbox": [86, 384, 541, 425], "spans": [{"bbox": [86, 384, 541, 425], "type": "text", "content": "If failure_criterion is 3, the Tsai- Hill criterion is used and six strength constants are needed: normal strengths in three directions and shear strengths in three principal planes, arranged as "}, {"bbox": [86, 384, 541, 425], "type": "inline_equation", "content": "X,Y,Z,R,T,S"}]}], "index": 7}, {"bbox": [86, 434, 541, 475], "type": "text", "lines": [{"bbox": [86, 434, 541, 475], "spans": [{"bbox": [86, 434, 541, 475], "type": "text", "content": "If failure_criterion is 4, the Tsai- Wu criterion is used and nine strength constants are needed: tensile strengths "}, {"bbox": [86, 434, 541, 475], "type": "inline_equation", "content": "(X,Y,Z)"}, {"bbox": [86, 434, 541, 475], "type": "text", "content": " , compressive strengths "}, {"bbox": [86, 434, 541, 475], "type": "inline_equation", "content": "(X^{\\prime},Y^{\\prime},Z^{\\prime})"}, {"bbox": [86, 434, 541, 475], "type": "text", "content": " in three directions, and shear strengths "}, {"bbox": [86, 434, 541, 475], "type": "inline_equation", "content": "(R,T,S)"}, {"bbox": [86, 434, 541, 475], "type": "text", "content": " in three principal planes, arranged as "}, {"bbox": [86, 434, 541, 475], "type": "inline_equation", "content": "X,Y,Z,<PERSON>^{\\prime},Y^{\\prime},Z^{\\prime},R,T,S"}]}], "index": 8}, {"bbox": [86, 483, 541, 525], "type": "text", "lines": [{"bbox": [86, 483, 541, 525], "spans": [{"bbox": [86, 483, 541, 525], "type": "text", "content": "If failure_criterion is 5, the <PERSON><PERSON> criterion is used and six strength constants are needed: tensile strengths "}, {"bbox": [86, 483, 541, 525], "type": "inline_equation", "content": "(X,Y)"}, {"bbox": [86, 483, 541, 525], "type": "text", "content": " , compressive strengths "}, {"bbox": [86, 483, 541, 525], "type": "inline_equation", "content": "(X^{\\prime},Y^{\\prime})"}, {"bbox": [86, 483, 541, 525], "type": "text", "content": " in two directions, and shear strengths "}, {"bbox": [86, 483, 541, 525], "type": "inline_equation", "content": "(R,S)"}, {"bbox": [86, 483, 541, 525], "type": "text", "content": " in two principal planes, arranged as "}, {"bbox": [86, 483, 541, 525], "type": "inline_equation", "content": "X,Y,X^{\\prime},Y^{\\prime},R,S"}]}], "index": 9}, {"bbox": [70, 533, 541, 587], "type": "text", "lines": [{"bbox": [70, 533, 541, 587], "spans": [{"bbox": [70, 533, 541, 587], "type": "text", "content": "It is noted that for failure analyses, general anisotropic materials are also approximated using orthotropic materials due to limited number of strength constants. In VABS, both the tensile strengths and compressive strengths are expressed using positive numbers. In other words, in the uniaxial compressive test along "}, {"bbox": [70, 533, 541, 587], "type": "inline_equation", "content": "y_{1}"}, {"bbox": [70, 533, 541, 587], "type": "text", "content": " direction, "}, {"bbox": [70, 533, 541, 587], "type": "inline_equation", "content": "\\sigma_{11} = - X^{\\prime}"}, {"bbox": [70, 533, 541, 587], "type": "text", "content": " when material fails."}]}], "index": 10}, {"bbox": [70, 601, 541, 654], "type": "text", "lines": [{"bbox": [70, 601, 541, 654], "spans": [{"bbox": [70, 601, 541, 654], "type": "text", "content": "The above block of data for strength properties for each material only needed if analysis=3. If analysis=1 or 2, the strength properties are not needed and should not be provided in the dehomogenization input file. Only the global beam responses as explained below should be stored in the dehomogenization input file."}]}], "index": 11}, {"bbox": [70, 668, 541, 696], "type": "text", "lines": [{"bbox": [70, 668, 541, 696], "spans": [{"bbox": [70, 668, 541, 696], "type": "text", "content": "The rest of inputs in the dehomogenization input file contains the global beam responses obtained from the 1D global beam analysis. To carry out a dehomogenization analysis based on the"}]}], "index": 12}], "discarded_blocks": [], "page_size": [612, 792], "page_idx": 6}, {"para_blocks": [{"bbox": [69, 104, 313, 118], "type": "text", "lines": [{"bbox": [69, 104, 313, 118], "spans": [{"bbox": [69, 104, 313, 118], "type": "text", "content": "classical model, VABS requires the following data:"}]}], "index": 0}, {"bbox": [256, 130, 354, 198], "type": "interline_equation", "lines": [{"bbox": [256, 130, 354, 198], "spans": [{"bbox": [256, 130, 354, 198], "type": "interline_equation", "content": "\\begin{array}{c c c}{{u_{1}}}&{{u_{2}}}&{{u_{3}}}\\\\ {{C_{11}}}&{{C_{12}}}&{{C_{13}}}\\\\ {{C_{21}}}&{{C_{22}}}&{{C_{23}}}\\\\ {{C_{31}}}&{{C_{32}}}&{{C_{33}}}\\\\ {{F_{1}}}&{{M_{1}}}&{{M_{2}}}&{{M_{3}}}\\end{array}", "image_path": "acdf107602d4019931bf248b4819533bbd22d87c535cc4cbdf7adb65ea17d89c.jpg"}]}], "index": 1}, {"bbox": [69, 205, 540, 233], "type": "text", "lines": [{"bbox": [69, 205, 540, 233], "spans": [{"bbox": [69, 205, 540, 233], "type": "text", "content": "where "}, {"bbox": [69, 205, 540, 233], "type": "inline_equation", "content": "u_{1}"}, {"bbox": [69, 205, 540, 233], "type": "text", "content": ", "}, {"bbox": [69, 205, 540, 233], "type": "inline_equation", "content": "u_{2}"}, {"bbox": [69, 205, 540, 233], "type": "text", "content": ", and "}, {"bbox": [69, 205, 540, 233], "type": "inline_equation", "content": "u_{3}"}, {"bbox": [69, 205, 540, 233], "type": "text", "content": " are the 1D beam displacements along "}, {"bbox": [69, 205, 540, 233], "type": "inline_equation", "content": "x_{1}"}, {"bbox": [69, 205, 540, 233], "type": "text", "content": ", "}, {"bbox": [69, 205, 540, 233], "type": "inline_equation", "content": "x_{2}"}, {"bbox": [69, 205, 540, 233], "type": "text", "content": ", "}, {"bbox": [69, 205, 540, 233], "type": "inline_equation", "content": "x_{3}"}, {"bbox": [69, 205, 540, 233], "type": "text", "content": ", respectively. The matrix "}, {"bbox": [69, 205, 540, 233], "type": "inline_equation", "content": "C_{ij}"}, {"bbox": [69, 205, 540, 233], "type": "text", "content": ", with "}, {"bbox": [69, 205, 540, 233], "type": "inline_equation", "content": "i = 1,2,3"}, {"bbox": [69, 205, 540, 233], "type": "text", "content": " and "}, {"bbox": [69, 205, 540, 233], "type": "inline_equation", "content": "j = 1,2,3"}, {"bbox": [69, 205, 540, 233], "type": "text", "content": ", is the direction cosine matrix defined as"}]}], "index": 2}, {"bbox": [197, 245, 414, 258], "type": "interline_equation", "lines": [{"bbox": [197, 245, 414, 258], "spans": [{"bbox": [197, 245, 414, 258], "type": "interline_equation", "content": "\\mathbf{B}_{i} = C_{i1} \\mathbf{b}_{1} + C_{i2} \\mathbf{b}_{2} + C_{i3} \\mathbf{b}_{3} \\text{with} i = 1,2,3", "image_path": "0ed977b897592f6cbd020bdfe610b912cac10bb263fb690b301914f13c724d4a.jpg"}]}], "index": 3}, {"bbox": [70, 267, 541, 377], "type": "text", "lines": [{"bbox": [70, 267, 541, 377], "spans": [{"bbox": [70, 267, 541, 377], "type": "text", "content": "where "}, {"bbox": [70, 267, 541, 377], "type": "inline_equation", "content": "\\mathbf{B}_{1}"}, {"bbox": [70, 267, 541, 377], "type": "text", "content": ", "}, {"bbox": [70, 267, 541, 377], "type": "inline_equation", "content": "\\mathbf{B}_{2}"}, {"bbox": [70, 267, 541, 377], "type": "text", "content": ", and "}, {"bbox": [70, 267, 541, 377], "type": "inline_equation", "content": "\\mathbf{B}_{3}"}, {"bbox": [70, 267, 541, 377], "type": "text", "content": " are the base vectors of the deformed beam and "}, {"bbox": [70, 267, 541, 377], "type": "inline_equation", "content": "\\mathbf{b}_{1}"}, {"bbox": [70, 267, 541, 377], "type": "text", "content": ", "}, {"bbox": [70, 267, 541, 377], "type": "inline_equation", "content": "\\mathbf{b}_{2}"}, {"bbox": [70, 267, 541, 377], "type": "text", "content": ", and "}, {"bbox": [70, 267, 541, 377], "type": "inline_equation", "content": "\\mathbf{b}_{3}"}, {"bbox": [70, 267, 541, 377], "type": "text", "content": " are the base vectors of the undeformed beam. Details of this definition can be found in Ref. [7]. "}, {"bbox": [70, 267, 541, 377], "type": "inline_equation", "content": "u_{i}"}, {"bbox": [70, 267, 541, 377], "type": "text", "content": " and "}, {"bbox": [70, 267, 541, 377], "type": "inline_equation", "content": "C_{ij}"}, {"bbox": [70, 267, 541, 377], "type": "text", "content": " are needed only for recovering 3D displacements. If the user is not interested in 3D displacements, these values can be arbitrary real numbers. "}, {"bbox": [70, 267, 541, 377], "type": "inline_equation", "content": "F_{1}"}, {"bbox": [70, 267, 541, 377], "type": "text", "content": " is the axial force, "}, {"bbox": [70, 267, 541, 377], "type": "inline_equation", "content": "M_{1}"}, {"bbox": [70, 267, 541, 377], "type": "text", "content": " is the torque, "}, {"bbox": [70, 267, 541, 377], "type": "inline_equation", "content": "M_{2}"}, {"bbox": [70, 267, 541, 377], "type": "text", "content": " is the bending moment around "}, {"bbox": [70, 267, 541, 377], "type": "inline_equation", "content": "x_{2}"}, {"bbox": [70, 267, 541, 377], "type": "text", "content": ", and "}, {"bbox": [70, 267, 541, 377], "type": "inline_equation", "content": "M_{3}"}, {"bbox": [70, 267, 541, 377], "type": "text", "content": " is the bending moment around "}, {"bbox": [70, 267, 541, 377], "type": "inline_equation", "content": "x_{3}"}, {"bbox": [70, 267, 541, 377], "type": "text", "content": ". The sectional stress resultants are needed for computing 3D stresses/strains/failure indexes/strength ratios within the cross- section. For example, if the user wants to compute these quantities under 1 unit tensile axial force along with 1 unit bending moment around "}, {"bbox": [70, 267, 541, 377], "type": "inline_equation", "content": "x_{2}"}, {"bbox": [70, 267, 541, 377], "type": "text", "content": ", the inputs can be arranged as:"}]}], "index": 4}, {"bbox": [277, 388, 333, 455], "type": "interline_equation", "lines": [{"bbox": [277, 388, 333, 455], "spans": [{"bbox": [277, 388, 333, 455], "type": "interline_equation", "content": "\\begin{array}{c c c}{{0}}&{{0}}&{{0}}\\\\ {{1}}&{{0}}&{{0}}\\\\ {{0}}&{{1}}&{{0}}\\\\ {{0}}&{{0}}&{{1}}\\\\ {{1}}&{{0}}&{{1}}&{{0}}\\end{array}", "image_path": "a94ac8b56c0b863583b405b661b5dc59f7ad8b8d84305c8225110f330d4d949f.jpg"}]}], "index": 5}, {"bbox": [69, 464, 542, 519], "type": "text", "lines": [{"bbox": [69, 464, 542, 519], "spans": [{"bbox": [69, 464, 542, 519], "type": "text", "content": "To perform dehomogenization for multiple load cases, the user needs to insert corresponding lines of "}, {"bbox": [69, 464, 542, 519], "type": "inline_equation", "content": "F_{1}, M_{1}, M_{2}, M_{3}"}, {"bbox": [69, 464, 542, 519], "type": "text", "content": " after the end of this block. For example, to perform dehomogenization for two more load cases with "}, {"bbox": [69, 464, 542, 519], "type": "inline_equation", "content": "F_{1} = 2"}, {"bbox": [69, 464, 542, 519], "type": "text", "content": ", "}, {"bbox": [69, 464, 542, 519], "type": "inline_equation", "content": "M_{1} = 2"}, {"bbox": [69, 464, 542, 519], "type": "text", "content": ", "}, {"bbox": [69, 464, 542, 519], "type": "inline_equation", "content": "M_{2} = M_{3} = 0"}, {"bbox": [69, 464, 542, 519], "type": "text", "content": " and "}, {"bbox": [69, 464, 542, 519], "type": "inline_equation", "content": "F_{1} = 2"}, {"bbox": [69, 464, 542, 519], "type": "text", "content": ", "}, {"bbox": [69, 464, 542, 519], "type": "inline_equation", "content": "M_{1} = 3"}, {"bbox": [69, 464, 542, 519], "type": "text", "content": ", "}, {"bbox": [69, 464, 542, 519], "type": "inline_equation", "content": "M_{3} = 4"}, {"bbox": [69, 464, 542, 519], "type": "text", "content": ", "}, {"bbox": [69, 464, 542, 519], "type": "inline_equation", "content": "M_{5} = 5"}, {"bbox": [69, 464, 542, 519], "type": "text", "content": ", we must provide the following inputs."}]}], "index": 6}, {"bbox": [277, 519, 334, 613], "type": "interline_equation", "lines": [{"bbox": [277, 519, 334, 613], "spans": [{"bbox": [277, 519, 334, 613], "type": "interline_equation", "content": "\\begin{array}{c c c}{{0}}&{{0}}&{{0}}\\\\ {{1}}&{{0}}&{{0}}\\\\ {{0}}&{{1}}&{{0}}\\\\ {{0}}&{{0}}&{{1}}\\\\ {{1}}&{{0}}&{{1}}&{{0}}\\\\ {{2}}&{{2}}&{{0}}&{{0}}\\\\ {{2}}&{{3}}&{{4}}&{{5}}\\end{array}", "image_path": "4e1d3fa92cca9863abeaea6e41c6ac7faeff0f7a4a09dccc4af01a2cbe630ede.jpg"}]}], "index": 7}, {"bbox": [84, 618, 541, 632], "type": "text", "lines": [{"bbox": [84, 618, 541, 632], "spans": [{"bbox": [84, 618, 541, 632], "type": "text", "content": "To carry out a dehomogenization analysis based on the <PERSON><PERSON><PERSON> model, VABS requires the"}]}], "index": 8}], "discarded_blocks": [], "page_size": [612, 792], "page_idx": 7}, {"para_blocks": [{"bbox": [70, 104, 142, 118], "type": "text", "lines": [{"bbox": [70, 104, 142, 118], "spans": [{"bbox": [70, 104, 142, 118], "type": "text", "content": "following data:"}]}], "index": 0}, {"bbox": [229, 120, 383, 255], "type": "interline_equation", "lines": [{"bbox": [229, 120, 383, 255], "spans": [{"bbox": [229, 120, 383, 255], "type": "interline_equation", "content": "\\begin{array}{r l}{u_{1}} & {u_{2}} & {u_{3}}\\\\ {C_{11}} & {C_{12}} & {C_{13}}\\\\ {C_{21}} & {C_{22}} & {C_{23}}\\\\ {C_{31}} & {C_{32}} & {C_{33}}\\\\ {F_{1}} & {M_{1}} & {M_{2}} & {M_{3}}\\\\ {F_{2}} & {F_{2}} & {}\\\\ {f_{1}} & {f_{2}} & {f_{3}} & {m_{1}} & {m_{2}} & {m_{3}}\\\\ {f_{1}^{\\prime}} & {f_{2}^{\\prime}} & {f_{3}^{\\prime}} & {m_{1}^{\\prime}} & {m_{2}^{\\prime}} & {m_{3}^{\\prime}}\\\\ {f_{1}^{\\prime \\prime}} & {f_{2}^{\\prime \\prime}} & {f_{3}^{\\prime \\prime}} & {m_{1}^{\\prime \\prime}} & {m_{2}^{\\prime \\prime}} & {m_{3}^{\\prime \\prime}}\\\\ {f_{1}^{\\prime \\prime \\prime}} & {f_{2}^{\\prime \\prime \\prime}} & {f_{3}^{\\prime \\prime \\prime}} & {m_{1}^{\\prime \\prime \\prime}} & {m_{2}^{\\prime \\prime \\prime}} & {m_{3}^{\\prime \\prime \\prime}}\\\\ {f_{1}^{\\prime \\prime \\prime}} & {f_{2}^{\\prime \\prime \\prime}} & {f_{3}^{\\prime \\prime \\prime}} & {m_{1}^{\\prime \\prime \\prime}} & {m_{2}^{\\prime \\prime \\prime}} & {m_{3}^{\\prime \\prime \\prime}} \\end{array}", "image_path": "12350bc44c9c546b99b21d7cad1180328f9e2b6085847f4616e62410f958bad0.jpg"}]}], "index": 1}, {"bbox": [70, 256, 541, 354], "type": "text", "lines": [{"bbox": [70, 256, 541, 354], "spans": [{"bbox": [70, 256, 541, 354], "type": "text", "content": "where the additional data "}, {"bbox": [70, 256, 541, 354], "type": "inline_equation", "content": "F_{2}"}, {"bbox": [70, 256, 541, 354], "type": "text", "content": " and "}, {"bbox": [70, 256, 541, 354], "type": "inline_equation", "content": "F_{3}"}, {"bbox": [70, 256, 541, 354], "type": "text", "content": " are transverse shear forces along "}, {"bbox": [70, 256, 541, 354], "type": "inline_equation", "content": "x_{2}"}, {"bbox": [70, 256, 541, 354], "type": "text", "content": " and "}, {"bbox": [70, 256, 541, 354], "type": "inline_equation", "content": "x_{3}"}, {"bbox": [70, 256, 541, 354], "type": "text", "content": " , respectively. "}, {"bbox": [70, 256, 541, 354], "type": "inline_equation", "content": "f_{1},f_{2},f_{3}"}, {"bbox": [70, 256, 541, 354], "type": "text", "content": " are distributed forces (including both applied forces and inertial forces) per unit span along "}, {"bbox": [70, 256, 541, 354], "type": "inline_equation", "content": "x_{1},x_{2},x_{3}"}, {"bbox": [70, 256, 541, 354], "type": "text", "content": " respectively. "}, {"bbox": [70, 256, 541, 354], "type": "inline_equation", "content": "m_{1},m_{2},m_{3}"}, {"bbox": [70, 256, 541, 354], "type": "text", "content": " are distributed moments (including both applied and inertial moments) per unit span along "}, {"bbox": [70, 256, 541, 354], "type": "inline_equation", "content": "x_{1},x_{2},x_{3}"}, {"bbox": [70, 256, 541, 354], "type": "text", "content": " respectively. The prime denotes derivative with respect to beam axis, that is "}, {"bbox": [70, 256, 541, 354], "type": "inline_equation", "content": "\\begin{array}{r}{()^{\\prime} = \\frac{\\partial}{\\partial x_{1}}} \\end{array}"}, {"bbox": [70, 256, 541, 354], "type": "inline_equation", "content": "\\begin{array}{r}{()^{\\prime \\prime} = \\frac{\\partial^{\\prime}}{x_{1}^{\\prime}}} \\end{array}"}, {"bbox": [70, 256, 541, 354], "type": "text", "content": " , and "}, {"bbox": [70, 256, 541, 354], "type": "inline_equation", "content": "\\begin{array}{r}{()^{\\prime \\prime \\prime} = \\frac{\\partial^{3}}{x_{1}^{3}}} \\end{array}"}, {"bbox": [70, 256, 541, 354], "type": "text", "content": " . If nload > 1, at the end of the above data block, we need to append two lines (one line for "}, {"bbox": [70, 256, 541, 354], "type": "inline_equation", "content": "F_{1},M_{1},M_{2},M_{3}"}, {"bbox": [70, 256, 541, 354], "type": "text", "content": " and one line for "}, {"bbox": [70, 256, 541, 354], "type": "inline_equation", "content": "F_{2},F_{3}"}, {"bbox": [70, 256, 541, 354], "type": "text", "content": " for each load case."}]}], "index": 2}, {"bbox": [70, 354, 540, 381], "type": "text", "lines": [{"bbox": [70, 354, 540, 381], "spans": [{"bbox": [70, 354, 540, 381], "type": "text", "content": "To carry out a dehomogenization analysis based on the <PERSON><PERSON>ov model, VABS requires the following data:"}]}], "index": 3}, {"bbox": [226, 384, 386, 451], "type": "interline_equation", "lines": [{"bbox": [226, 384, 386, 451], "spans": [{"bbox": [226, 384, 386, 451], "type": "interline_equation", "content": "\\begin{array}{r l r l r l r l r l} & {u_{1}} & {u_{2}} & {u_{3}} & & & & \\\\ & {C_{11}} & {C_{12}} & {C_{13}} & & & & \\\\ & {C_{21}} & {C_{22}} & {C_{23}} & & & & \\\\ & {C_{31}} & {C_{32}} & {C_{33}} & & & & \\\\ & {\\bar{\\gamma}_{11}} & {\\bar{\\kappa}_{1}} & {\\bar{\\kappa}_{2}} & {\\bar{\\kappa}_{3}} & {\\bar{\\kappa}_{1}^{\\prime}} & {\\bar{\\kappa}_{1}^{\\prime \\prime}} & {\\bar{\\kappa}_{1}^{\\prime \\prime \\prime}} & \\\\ & {\\bar{\\gamma}_{11}} & {\\bar{\\kappa}_{1}} & {\\bar{\\kappa}_{2}} & {\\bar{\\kappa}_{3}} & {\\bar{\\kappa}_{3}} & {\\bar{\\kappa}_{1}^{\\prime}} & {\\bar{\\kappa}_{1}^{\\prime \\prime}} & \\end{array}", "image_path": "a38bb3d57c739b52f81c25523a0cd00b3b76acf35128e8761b6134f031e9e611.jpg"}]}], "index": 4}, {"bbox": [70, 453, 540, 479], "type": "text", "lines": [{"bbox": [70, 453, 540, 479], "spans": [{"bbox": [70, 453, 540, 479], "type": "text", "content": "where "}, {"bbox": [70, 453, 540, 479], "type": "inline_equation", "content": "\\bar{\\gamma}_{11}"}, {"bbox": [70, 453, 540, 479], "type": "text", "content": " is the beam axial strain, "}, {"bbox": [70, 453, 540, 479], "type": "inline_equation", "content": "\\bar{\\kappa}_{1}"}, {"bbox": [70, 453, 540, 479], "type": "text", "content": " is the twist , "}, {"bbox": [70, 453, 540, 479], "type": "inline_equation", "content": "\\bar{\\kappa}_{2}"}, {"bbox": [70, 453, 540, 479], "type": "text", "content": " and "}, {"bbox": [70, 453, 540, 479], "type": "inline_equation", "content": "\\bar{\\kappa}_{3}"}, {"bbox": [70, 453, 540, 479], "type": "text", "content": " are the curvatures around "}, {"bbox": [70, 453, 540, 479], "type": "inline_equation", "content": "x_{2}"}, {"bbox": [70, 453, 540, 479], "type": "text", "content": " and "}, {"bbox": [70, 453, 540, 479], "type": "inline_equation", "content": "x_{3}"}, {"bbox": [70, 453, 540, 479], "type": "text", "content": " respectively."}]}], "index": 5}, {"bbox": [70, 479, 541, 548], "type": "text", "lines": [{"bbox": [70, 479, 541, 548], "spans": [{"bbox": [70, 479, 541, 548], "type": "text", "content": "It is noted that the global behavior needed for dehomogenization analyses should not violate the small strain assumption. Otherwise, you might get some unexpected results. For example, if your transverse shear stiffness is 2.5 N, then inputting a shear force resultant of 1 N is too large as the shear strain will be about 0.4, which cannot be considered as small, the basic assumption of the VABS theory."}]}], "index": 6}, {"bbox": [70, 560, 541, 642], "type": "text", "lines": [{"bbox": [70, 560, 541, 642], "spans": [{"bbox": [70, 560, 541, 642], "type": "text", "content": "Both input files, input_file_name and input_file_name.glb, should be ended with a blank line to avoid any possible incompatibility of different computer operating systems. The input file can be given any name as long as the total number of the characters of the name including extension is not more than 256. For the convenience of the user to identify mistakes in the input file, all the inputs are echoed in the file named input_file_name.ech. Error messages are also written at the end of input_file_name.ech and on the output screen."}]}], "index": 7}, {"bbox": [70, 655, 280, 670], "type": "title", "lines": [{"bbox": [70, 655, 280, 670], "spans": [{"bbox": [70, 655, 280, 670], "type": "text", "content": "7.3 User-defined Failure Criterion"}]}], "index": 8, "level": 2, "line_avg_height": 19}, {"bbox": [70, 677, 541, 704], "type": "text", "lines": [{"bbox": [70, 677, 541, 704], "spans": [{"bbox": [70, 677, 541, 704], "type": "text", "content": "A simple UMAT is provided for users to program their own failure criterion by changing the fortran code UserFC.f90. The sample code is for max stress failure criterion for anisotropic materials. The"}]}], "index": 9}], "discarded_blocks": [], "page_size": [612, 792], "page_idx": 8}, {"para_blocks": [{"bbox": [70, 104, 541, 227], "type": "text", "lines": [{"bbox": [70, 104, 541, 227], "spans": [{"bbox": [70, 104, 541, 227], "type": "text", "content": "failure criterion number and strength constants should be provided as described above. Pointwise strains and stresses and strength constants are passed to the user subroutine, failure index, strength ratio, failure mode, and strength ratio for each stress component are computed inside this subroutine and passed back to VABS. Users need to first modify UserFC.f90 according to their own failure criterion, then compile it to be a shared library. A sample make file (MakeUser) for using the fortran compiler fort is included. One only needs to execute make - f MakeUser to compile the user subroutine. Then, one can use VABS with user- defined failure criterion. The code is simple enough and there is enough comments inside the source codes for the user to adopt the sample code for other user- defined failure criterion."}]}], "index": 0}, {"bbox": [70, 244, 203, 261], "type": "title", "lines": [{"bbox": [70, 244, 203, 261], "spans": [{"bbox": [70, 244, 203, 261], "type": "text", "content": "8 VABS Outputs"}]}], "index": 1, "level": 1, "line_avg_height": 22}, {"bbox": [70, 271, 541, 326], "type": "text", "lines": [{"bbox": [70, 271, 541, 326], "spans": [{"bbox": [70, 271, 541, 326], "type": "text", "content": "VABS homogenization analysis outputs the sectional properties stored in a text file named input_file_name.K. VABS dehomogenization analysis could output 3D displacement/strain/stress, or failure index/strength ratio distributions over the cross- section in different files as explained later. All these output files are in pure text format and can be opened by any text editor."}]}], "index": 2}, {"bbox": [70, 340, 251, 354], "type": "title", "lines": [{"bbox": [70, 340, 251, 354], "spans": [{"bbox": [70, 340, 251, 354], "type": "text", "content": "8.1 Homogenization Outputs"}]}], "index": 3, "level": 2, "line_avg_height": 18}, {"bbox": [70, 361, 541, 416], "type": "text", "lines": [{"bbox": [70, 361, 541, 416], "spans": [{"bbox": [70, 361, 541, 416], "type": "text", "content": "Sectional properties obtained by a VABS homogenization analysis are stored in input_file_name.K. Some results are listed as individual numbers, and some are listed as matrices. The definitions of these properties are briefly summarized here for the convenience of end users. For more details, please refer to VABS related publications."}]}], "index": 4}, {"bbox": [70, 430, 541, 456], "type": "text", "lines": [{"bbox": [70, 430, 541, 456], "spans": [{"bbox": [70, 430, 541, 456], "type": "text", "content": "VABS first computes the inertial properties which is represented by a "}, {"bbox": [70, 430, 541, 456], "type": "inline_equation", "content": "6\\times 6"}, {"bbox": [70, 430, 541, 456], "type": "text", "content": " mass matrix with respect to the beam coordinate system. The elements of the mass matrix are arranged as"}]}], "index": 5}, {"bbox": [174, 465, 436, 549], "type": "interline_equation", "lines": [{"bbox": [174, 465, 436, 549], "spans": [{"bbox": [174, 465, 436, 549], "type": "interline_equation", "content": "\\left[ \\begin{array}{c c c c c c}{\\mu} & 0 & 0 & 0 & {\\mu x_{M3}} & {-\\mu x_{M2}}\\\\ 0 & \\mu & 0 & {-\\mu x_{M3}} & 0 & 0\\\\ 0 & 0 & \\mu & {\\mu x_{M2}} & 0 & 0\\\\ 0 & {-\\mu x_{M3}} & {\\mu x_{M2}} & {i_{22} + i_{33}} & 0 & 0\\\\ {\\mu x_{M3}} & 0 & 0 & 0 & {i_{22}} & {i_{23}}\\\\ {-\\mu x_{M2}} & 0 & 0 & 0 & {i_{23}} & {i_{33}} \\end{array} \\right]", "image_path": "6f16f84c6ec80afc851d69df5c9c7519383378c5d8f53001e3201353bcaf2521.jpg"}]}], "index": 6}, {"bbox": [70, 555, 541, 692], "type": "text", "lines": [{"bbox": [70, 555, 541, 692], "spans": [{"bbox": [70, 555, 541, 692], "type": "text", "content": "where "}, {"bbox": [70, 555, 541, 692], "type": "inline_equation", "content": "\\mu"}, {"bbox": [70, 555, 541, 692], "type": "text", "content": " is mass per unit length, "}, {"bbox": [70, 555, 541, 692], "type": "inline_equation", "content": "x_{M2}"}, {"bbox": [70, 555, 541, 692], "type": "text", "content": " and "}, {"bbox": [70, 555, 541, 692], "type": "inline_equation", "content": "x_{M3}"}, {"bbox": [70, 555, 541, 692], "type": "text", "content": " are the two coordinates of the mass center (also called the mass- weighted centroid), and "}, {"bbox": [70, 555, 541, 692], "type": "inline_equation", "content": "i_{22}, i_{23}"}, {"bbox": [70, 555, 541, 692], "type": "text", "content": " and "}, {"bbox": [70, 555, 541, 692], "type": "inline_equation", "content": "i_{33}"}, {"bbox": [70, 555, 541, 692], "type": "text", "content": " are the second mass moments of inertia. The mass center and mass moments of inertia are measured with respect to the origin "}, {"bbox": [70, 555, 541, 692], "type": "inline_equation", "content": "O"}, {"bbox": [70, 555, 541, 692], "type": "text", "content": " and coordinate axes "}, {"bbox": [70, 555, 541, 692], "type": "inline_equation", "content": "x_{2}"}, {"bbox": [70, 555, 541, 692], "type": "text", "content": " and "}, {"bbox": [70, 555, 541, 692], "type": "inline_equation", "content": "x_{3}"}, {"bbox": [70, 555, 541, 692], "type": "text", "content": ". VABS also outputs the mass center, the mass matrix measured with respect to the coordinate system with the mass center as the origin and coordinates parallel to the beam coordinate system. Furthermore, VABS also outputs the inertial properties with respect to the principal inertial coordinate system (origin at the mass center, coordinates aligning with the principal inertial axes) including a mass per unit length, mass moments of inertia about the three axes, the orientation of the principal inertial axes, and mass- weighted radius of gyration (defined as the square root of the mass moment of inertia about "}, {"bbox": [70, 555, 541, 692], "type": "inline_equation", "content": "x_{1}"}, {"bbox": [70, 555, 541, 692], "type": "text", "content": " divided by the mass per unit length)."}]}], "index": 7}], "discarded_blocks": [], "page_size": [612, 792], "page_idx": 9}], "_backend": "vlm", "_version_name": "2.1.10"}