[project]
name = "pyvabs"
version = "0.1.0"
description = "A Python library for [add your description here]"
readme = "README.md"
authors = [
    { name = "<PERSON> T<PERSON>", email = "<EMAIL>" }
]
requires-python = ">=3.13"
dependencies = [
    "numpy>=1.24.0",
]
keywords = ["python", "library"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.13",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0",
    "pytest-cov>=4.0",
    "black>=23.0",
    "isort>=5.0",
    "flake8>=6.0",
    "mypy>=1.0",
    "pre-commit>=3.0",
]
docs = [
    "sphinx>=7.0",
    "sphinx-rtd-theme>=1.0",
    "myst-parser>=2.0",
]

[project.urls]
Homepage = "https://github.com/yourusername/pyvabs"
Repository = "https://github.com/yourusername/pyvabs"
Documentation = "https://pyvabs.readthedocs.io"
Issues = "https://github.com/yourusername/pyvabs/issues"

[build-system]
requires = ["uv_build>=0.8.4,<0.9.0"]
build-backend = "uv_build"

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--cov=src/pyvabs",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
]

[tool.black]
line-length = 88
target-version = ["py313"]
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["pyvabs"]

[tool.mypy]
python_version = "3.13"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = "tests.*"
disallow_untyped_defs = false
