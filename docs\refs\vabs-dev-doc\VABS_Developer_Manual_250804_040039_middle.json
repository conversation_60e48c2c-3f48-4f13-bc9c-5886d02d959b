{"pdf_info": [{"para_blocks": [{"bbox": [187, 72, 423, 92], "type": "title", "lines": [{"bbox": [187, 72, 423, 92], "spans": [{"bbox": [187, 72, 423, 92], "type": "text", "content": "VABS Manual for Developers<sup>1</sup>"}]}], "index": 0, "level": 1, "line_avg_height": 26}, {"bbox": [89, 120, 178, 136], "type": "title", "lines": [{"bbox": [89, 120, 178, 136], "spans": [{"bbox": [89, 120, 178, 136], "type": "text", "content": "1. Introduction"}]}], "index": 1, "level": 2, "line_avg_height": 18}, {"bbox": [88, 152, 523, 179], "type": "text", "lines": [{"bbox": [88, 152, 523, 179], "spans": [{"bbox": [88, 152, 523, 179], "type": "text", "content": "VABS can be used as either a callable library or a standalone application, depending on the need. The program flow of both applications is the same and is illustrated in Figure 1."}]}], "index": 2}, {"bbox": [88, 193, 523, 331], "type": "text", "lines": [{"bbox": [88, 193, 523, 331], "spans": [{"bbox": [88, 193, 523, 331], "type": "text", "content": "The yellow boxes are subroutines handling I/O needed for VABS. VABS needs to read the cross- sectional model which is generated by a preprocessor (the first yellow box). The inputs should include problem control parameters, mesh control parameters, nodal coordinates, elemental connectivity, layup information, and material properties. For the dehomogenization analysis, VABS should also read strength properties for each material and global beam behavior calculated by the 1D beam analysis and the warping functions and constitutive models calculated in the homogenization analysis. The outputs of the homogenization analysis include inertial properties and structural properties and corresponding warping functions. The outputs of the dehomogenization analysis include the 3D displacements, stresses, strains, failure indexes, and strength ratios."}]}], "index": 3}, {"type": "image", "bbox": [145, 337, 487, 594], "blocks": [{"bbox": [145, 337, 487, 594], "lines": [{"bbox": [145, 337, 487, 594], "spans": [{"bbox": [145, 337, 487, 594], "type": "image", "image_path": "39810bfe46deb709425156025e36dd1a32728692a8870fcd08052b21f6dc03b4.jpg"}]}], "index": 4, "type": "image_body"}, {"bbox": [228, 600, 426, 614], "lines": [{"bbox": [228, 600, 426, 614], "spans": [{"bbox": [228, 600, 426, 614], "type": "text", "content": "Figure 1. Modified VABS code structure"}]}], "index": 5, "type": "image_caption"}], "index": 4}, {"bbox": [87, 621, 523, 649], "type": "text", "lines": [{"bbox": [87, 621, 523, 649], "spans": [{"bbox": [87, 621, 523, 649], "type": "text", "content": "The red boxes including homogenization and dehomogenization are handled by two separated subroutines contained in VABSLib.dll. These two subroutines contain all"}]}], "index": 6}], "discarded_blocks": [], "page_size": [612, 792], "page_idx": 0}, {"para_blocks": [{"bbox": [88, 72, 523, 126], "type": "text", "lines": [{"bbox": [88, 72, 523, 126], "spans": [{"bbox": [88, 72, 523, 126], "type": "text", "content": "analysis capabilities of VABS. For design environment developments, these two subroutines are like two plug- n- play black boxes. What the developers need to provide are interfaces to call these two subroutines and communicate with the calling environment."}]}], "index": 0}, {"bbox": [88, 140, 307, 156], "type": "title", "lines": [{"bbox": [88, 140, 307, 156], "spans": [{"bbox": [88, 140, 307, 156], "type": "text", "content": "2. Global Variables needed for VABS"}]}], "index": 1, "level": 2, "line_avg_height": 17}, {"bbox": [88, 172, 522, 213], "type": "text", "lines": [{"bbox": [88, 172, 522, 213], "spans": [{"bbox": [88, 172, 522, 213], "type": "text", "content": "GlobalDataFunE.f90 defines the global variables for VABS, although they are not passed to/from the two DLLs. They are necessary for defining the variables passing to/from the two subroutines. These variables are"}]}], "index": 2}, {"bbox": [105, 216, 525, 331], "type": "text", "lines": [{"bbox": [105, 216, 525, 331], "spans": [{"bbox": [105, 216, 525, 331], "type": "text", "content": "allo_stat: an integer variable to indicate status of allocating memory in_stat: an integer variable to indicate status of I/O process DBL: an integer constant to indicate how many digits real numbers should be used. For double precision "}, {"bbox": [105, 216, 525, 331], "type": "inline_equation", "content": "\\mathrm{DBL} = 8"}, {"bbox": [105, 216, 525, 331], "type": "text", "content": " for single precision "}, {"bbox": [105, 216, 525, 331], "type": "inline_equation", "content": "\\mathrm{DBL} = 4"}, {"bbox": [105, 216, 525, 331], "type": "text", "content": " TOLERANCE: a real constant to simulate a small number NDIM, an integer constant equal to 2 MAX_NODE_ELEM, an integer constant equal to 9 NE_ID, an integer constant equal to 4"}]}], "index": 3}, {"bbox": [88, 345, 301, 360], "type": "title", "lines": [{"bbox": [88, 345, 301, 360], "spans": [{"bbox": [88, 345, 301, 360], "type": "text", "content": "3. I/O Variables for Homogenization"}]}], "index": 4, "level": 2, "line_avg_height": 21}, {"bbox": [88, 376, 523, 418], "type": "text", "lines": [{"bbox": [88, 376, 523, 418], "spans": [{"bbox": [88, 376, 523, 418], "type": "text", "content": "To per homogenization, one needs to call the subroutine ConstitutiveModeling, which implies the right arguments should pass to and from this subroutine. The subroutine is invoked as follows (in the format of Fortran 90/95):"}]}], "index": 5}, {"bbox": [88, 431, 523, 502], "type": "text", "lines": [{"bbox": [88, 431, 523, 502], "spans": [{"bbox": [88, 431, 523, 502], "type": "text", "content": "CALL ConstitutiveModeling (inp_name, format_I, mat_type_layer, layup_angle, LAY_CONST, nlayer, & <PERSON><PERSON>enko_I, curved_I, oblique_I, trapeze_I, <PERSON><PERSON>ov_I, damping_I, kb, beta, & nncide, nelem, nmate, coord, element, layup, mat_type, material, orth, density, damping, & damping_layer, mass, area, xm2, xm3, mass_mc, I22, I33, mass_angle, Xg2, Xg3, Aee_Aee_F, Xe2, & Xe3, Aee_k, Aee_k_F, Aee_damp, Xe2_k, Xe3_k, ST, ST_F, ST_damp, Sc1, Sc2, stiff_val, & stiff_val_F, stiff_val_damp, Ag1, Bk1, Ck2, Dk3, thermal_I, cte, temperature, NT, NT_F, error)"}]}], "index": 6}, {"bbox": [88, 515, 347, 529], "type": "text", "lines": [{"bbox": [88, 515, 347, 529], "spans": [{"bbox": [88, 515, 347, 529], "type": "text", "content": "with the following variables passed to this subroutine"}]}], "index": 7}, {"bbox": [105, 530, 523, 714], "type": "text", "lines": [{"bbox": [105, 530, 523, 714], "spans": [{"bbox": [105, 530, 523, 714], "type": "text", "content": "- inp_name is a character of length 256 denoting the homogenization input file name for VABS.- format_I is a integer indicating the input format with value equal to 1 or others.- mat_type_layer is a integer array with dimension as nlayer, indicating the material type for each layer.- layup_angle is a real array with dimension nlayer, indicating the layup orientation for each layer.- LAY_CONST is an integer equal to 1 for new format or equal to 10 for old format.- nlayer is an integer denoting the number of layers.- <PERSON><PERSON><PERSON>_I, curved_I, oblique_I, trapeze_I, <PERSON><PERSON>ov_I, damping_I are six integer variables with values being 0 or 1.- kb is a 1D real array with dimension as 3 holding three numbers for initial twist "}, {"bbox": [105, 530, 523, 714], "type": "inline_equation", "content": "(k_{1})"}, {"bbox": [105, 530, 523, 714], "type": "text", "content": " and initial curvatures "}, {"bbox": [105, 530, 523, 714], "type": "inline_equation", "content": "(k_{2}, k_{3})"}, {"bbox": [105, 530, 523, 714], "type": "text", "content": "."}]}], "index": 8}], "discarded_blocks": [], "page_size": [612, 792], "page_idx": 1}, {"para_blocks": [{"bbox": [105, 72, 524, 507], "type": "text", "lines": [{"bbox": [105, 72, 524, 507], "spans": [{"bbox": [105, 72, 524, 507], "type": "text", "content": "- beta is a 1D real array with dimension as 3 holding three numbers including the two oblique parameters plus a zero at the "}, {"bbox": [105, 72, 524, 507], "type": "inline_equation", "content": "3^{\\mathrm{rd}}"}, {"bbox": [105, 72, 524, 507], "type": "text", "content": " position.- nnode is an integer number for total number of nodes.- nelem is an integer number for total number of element.- nmate is an integer number for total number of material types.- coord is a 2D real array with dimension as (nnode, NDIM). The "}, {"bbox": [105, 72, 524, 507], "type": "inline_equation", "content": "x_{2}"}, {"bbox": [105, 72, 524, 507], "type": "text", "content": " and "}, {"bbox": [105, 72, 524, 507], "type": "inline_equation", "content": "x_{3}"}, {"bbox": [105, 72, 524, 507], "type": "text", "content": " coordinates of the "}, {"bbox": [105, 72, 524, 507], "type": "inline_equation", "content": "i"}, {"bbox": [105, 72, 524, 507], "type": "text", "content": "th node are stored in coord(i,1) and coord(i,2), respectively. Note these values are changed after execution due to optimizing the input mesh.- element is a 2D integer array with dimension as (nelem, MAX_NODE_ELEM) holding the ten integers needed for elemental connectivity. Note these values are changed after execution due to optimizing the input mesh.- layup is a 2D real array with dimension as (nelem, LAY_CONST) holding the ten real numbers including one number for "}, {"bbox": [105, 72, 524, 507], "type": "inline_equation", "content": "\\theta_{3}"}, {"bbox": [105, 72, 524, 507], "type": "text", "content": " and nine numbers for "}, {"bbox": [105, 72, 524, 507], "type": "inline_equation", "content": "\\theta_{1}"}, {"bbox": [105, 72, 524, 507], "type": "text", "content": ".- mat_type is a 1D integer array with dimension as <PERSON><PERSON> holding the material type (or layer type for new format) for each element.- material is a 2D real array with dimension as (nmate,21) holding up to 21 real numbers for the elastic constants of each material.- orth is a 1D integer array with dimension as nmate holding an integer to indicate the anisotropy of the material.- density is a 1D real array with dimension as nmate holding density for each material.- damping is a 1D real array with dimension as nmate holding the damping coefficient for each material.- damping_layer is a 1D real array with dimension as nlayer holding the damping coefficient for each layer.- thermal_I is an integer variable with values being 0 or 1.- cte is a 2D real array with dimension as (nmate,6) holding the coefficients of thermal expansion.- temperature is a 1D real array with dimension as nnode holding the nodal temperature."}]}], "index": 0}, {"bbox": [88, 519, 429, 534], "type": "text", "lines": [{"bbox": [88, 519, 429, 534], "spans": [{"bbox": [88, 519, 429, 534], "type": "text", "content": "4. The following variables are passed from ConstitutiveModeling"}]}], "index": 1}, {"bbox": [105, 550, 524, 709], "type": "text", "lines": [{"bbox": [105, 550, 524, 709], "spans": [{"bbox": [105, 550, 524, 709], "type": "text", "content": "- mass is a 2D real array with dimension as (6,6) storing the mass matrix.- area is a real number for the cross-sectional area.- "}, {"bbox": [105, 550, 524, 709], "type": "inline_equation", "content": "x_{m2}"}, {"bbox": [105, 550, 524, 709], "type": "text", "content": ", "}, {"bbox": [105, 550, 524, 709], "type": "inline_equation", "content": "x_{m3}"}, {"bbox": [105, 550, 524, 709], "type": "text", "content": " are two real numbers for the mass center location.- mass_mc is a 2D real array with dimension as (6,6) storing the mass matrix at the mass center.- I22, I33 are two real numbers for mass moments of inertia about x2 and x3, respectively.- mass_angle is a real number for the angle of principal inertial axes in degrees.- "}, {"bbox": [105, 550, 524, 709], "type": "inline_equation", "content": "Xg2"}, {"bbox": [105, 550, 524, 709], "type": "text", "content": ", "}, {"bbox": [105, 550, 524, 709], "type": "inline_equation", "content": "Xg3"}, {"bbox": [105, 550, 524, 709], "type": "text", "content": " are two real numbers for the geometry center location.- Aee, Aee_F are two real arrays with dimension as (NE_ID,NE_ID) storing the zeroth-"}]}], "index": 2}], "discarded_blocks": [], "page_size": [612, 792], "page_idx": 2}, {"para_blocks": [{"bbox": [105, 72, 523, 427], "type": "text", "lines": [{"bbox": [105, 72, 523, 427], "spans": [{"bbox": [105, 72, 523, 427], "type": "text", "content": "order stiffness matrix and flexibility matrix, respectively. Xe2, Xe3 are two real numbers for the tension center location. Aee_k, Aee_k_F are two real arrays with dimension as (NE_1D,NE_1D) storing the stiffness matrix and flexibility matrix due to correction of initial twist/curvature, respectively. Aee_damp is a real array with dimension as (NE_1D,NE_1D) storing the sectional damping matrix corresponding to the classical beam model. Xe2_k, Xe3_k are two real numbers for the tension center location with corrections from initial twist/curvatures. ST, ST_F are two real arrays with dimension as (6,6) storing the stiffness matrix and flexibility matrix for the Timoshenko model, respectively. ST_damp is a real array with dimension as (6,6) storing the sectional damping matrix corresponding to the <PERSON><PERSON>enko model. Sc1, Sc2 are two real numbers for the shear center location. stiff_val, stiff_val_F are two real arrays with dimension as (5,5) storing the stiffness matrix and flexibility matrix for the Vlasov model, respectively. stiff_val_damp is a real array with dimension as (5,5) storing the sectional damping corresponding to the Vlasov model. Agl, Bk1, Ck2, Dk3 are four real arrays with dimension as (4,4) storing the matrices for Trapeze effects. NT is a 1D real array with dimension as 4 storing the 1D nonmechanical stress resultants. NT_F is a 1D real array with dimension as 4 storing the 1D thermal strains. error is a character variable with length 300 to store the error message of the program."}]}], "index": 0}, {"bbox": [88, 443, 315, 457], "type": "title", "lines": [{"bbox": [88, 443, 315, 457], "spans": [{"bbox": [88, 443, 315, 457], "type": "text", "content": "5. I/O Variables for Dehomogenization"}]}], "index": 1, "level": 2, "line_avg_height": 21}, {"bbox": [88, 459, 523, 498], "type": "text", "lines": [{"bbox": [88, 459, 523, 498], "spans": [{"bbox": [88, 459, 523, 498], "type": "text", "content": "To use the dehomogenization capability of VABS, one needs to call the Recovery subroutine, which implies the right arguments should pass to and from this subroutine. This subroutine is invoked as follows (in the format of Fortran 90/95):"}]}], "index": 2}, {"bbox": [88, 515, 523, 605], "type": "text", "lines": [{"bbox": [88, 515, 523, 605], "spans": [{"bbox": [88, 515, 523, 605], "type": "text", "content": "CALL Recovery (inp_name, format_I, mat_type_layer, layup_angle, LAY_CONST, nlayer, & recover_I, <PERSON><PERSON><PERSON>_<PERSON>, curved_I, oblique_I, <PERSON><PERSON><PERSON>_I, damping_I, kb, beta, & nnode, nelem, nmate, coord, element, layup, mat_type, material, orth, density, damping, & damping_layer, disp_1D, dir_cos_1D, strain_CL, strain_CL_1, strain_CL_2, & force_1D, load_1D, load1_1D, load2_1D, & disp_3D_F, k_F, nd_F, ss_F, ss_nd_F, ss_elem, thermal_I, cte, temperature, load3_1D, strain_CL_3, error, fc, strength_constants, fi_pos, mode_pos, nload, str_comp)"}]}], "index": 3}, {"bbox": [88, 618, 523, 715], "type": "text", "lines": [{"bbox": [88, 618, 523, 715], "spans": [{"bbox": [88, 618, 523, 715], "type": "text", "content": "where inp_name, format_I, mat_type_layer, layup_angle, LAY_CONST, nlayer, recover_I, <PERSON><PERSON><PERSON>_<PERSON>, curved_I, oblique_I, <PERSON><PERSON>ov_I, damping_I, kb, beta, nnode, nelem, nmate, coord, element, layup, mat_type, material, orth, density, damping, damping_layer, disp_1D, dir_cos_1D, strain_CL, strain_CL_1, strain_CL_2, force_1D, load_1D, load1_1D, load2_1D, thermal_I, cte, temperature, load3_1D, strain_CL_3, fc, strength_constants are inputs to this subroutine. In addition to those variables common to the ConstitutiveModeling subroutine, the additional variables are defined as follows:"}]}], "index": 4}], "discarded_blocks": [], "page_size": [612, 792], "page_idx": 3}, {"para_blocks": [{"bbox": [105, 72, 523, 524], "type": "text", "lines": [{"bbox": [105, 72, 523, 524], "spans": [{"bbox": [105, 72, 523, 524], "type": "text", "content": "recover "}, {"bbox": [105, 72, 523, 524], "type": "inline_equation", "content": "I"}, {"bbox": [105, 72, 523, 524], "type": "text", "content": " is an integer flag whether the 3D fields are recovered using a nonlinear (1) or linear theory (2), or whether to perform failure analysis (3). This value is the same as the command line argument value. disp "}, {"bbox": [105, 72, 523, 524], "type": "inline_equation", "content": "ID"}, {"bbox": [105, 72, 523, 524], "type": "text", "content": " is a 1D real array with dimension as 3 holding three numbers for the global beam displacements. dir_cos "}, {"bbox": [105, 72, 523, 524], "type": "inline_equation", "content": "ID"}, {"bbox": [105, 72, 523, 524], "type": "text", "content": " is a 2D real array with dimensions as (3, 3) holding the 1D direction cosine matrix. strain "}, {"bbox": [105, 72, 523, 524], "type": "inline_equation", "content": "CL"}, {"bbox": [105, 72, 523, 524], "type": "text", "content": " is a 1D real array with dimension as 4 holding the four classical strain measures for <PERSON><PERSON>ov modeling. For other modeling, they are zeroes. strain "}, {"bbox": [105, 72, 523, 524], "type": "inline_equation", "content": "CL\\_ I"}, {"bbox": [105, 72, 523, 524], "type": "text", "content": " is a 1D real array with dimension as 4 with strain "}, {"bbox": [105, 72, 523, 524], "type": "inline_equation", "content": "\\mathrm{CL\\_1(2)}"}, {"bbox": [105, 72, 523, 524], "type": "text", "content": " holding the first derivative of the twist rate and all others are zeros for <PERSON><PERSON><PERSON> modeling. For other modeling, they are zeroes. strain "}, {"bbox": [105, 72, 523, 524], "type": "inline_equation", "content": "CL\\_ 2"}, {"bbox": [105, 72, 523, 524], "type": "text", "content": " is a 1D real array with dimension as 4 with strain "}, {"bbox": [105, 72, 523, 524], "type": "inline_equation", "content": "\\mathrm{CL\\_2(2)}"}, {"bbox": [105, 72, 523, 524], "type": "text", "content": " holding the second derivative of the twist rate and all others are zeros for <PERSON><PERSON><PERSON> modeling. For other modeling, they are zeroes. force "}, {"bbox": [105, 72, 523, 524], "type": "inline_equation", "content": "ID"}, {"bbox": [105, 72, 523, 524], "type": "text", "content": " is a 1D real array with dimension as 6, holding the 1D stress resultants with force_1D(1)=F1, force_1D(2)=F2, force_1D(3)=F3, force_1D(4)=M1, force_1D(5)=M2, force_1D(6)=M3. load "}, {"bbox": [105, 72, 523, 524], "type": "inline_equation", "content": "ID"}, {"bbox": [105, 72, 523, 524], "type": "text", "content": " is a 1D real array with dimension as 6, holding the 1D distributed loads load1 "}, {"bbox": [105, 72, 523, 524], "type": "inline_equation", "content": "ID"}, {"bbox": [105, 72, 523, 524], "type": "text", "content": " is a 1D real array with dimension as 6, holding the first derivatives of 1D distributed loads load2 "}, {"bbox": [105, 72, 523, 524], "type": "inline_equation", "content": "ID"}, {"bbox": [105, 72, 523, 524], "type": "text", "content": " is a 1D real array with dimension as 6, holding the second derivatives of 1D distributed loads load3 "}, {"bbox": [105, 72, 523, 524], "type": "inline_equation", "content": "ID"}, {"bbox": [105, 72, 523, 524], "type": "text", "content": " is a 1D real array with dimension as 6, holding the third derivatives of 1D distributed loads strain "}, {"bbox": [105, 72, 523, 524], "type": "inline_equation", "content": "CL\\_ 3"}, {"bbox": [105, 72, 523, 524], "type": "text", "content": " is a 1D real array with dimension as 4 with strain "}, {"bbox": [105, 72, 523, 524], "type": "inline_equation", "content": "\\mathrm{CL\\_3(2)}"}, {"bbox": [105, 72, 523, 524], "type": "text", "content": " holding the third derivative of the twist rate and all others are zeros for <PERSON><PERSON><PERSON> modeling. For other modeling, they are zeroes. "}, {"bbox": [105, 72, 523, 524], "type": "inline_equation", "content": "fc"}, {"bbox": [105, 72, 523, 524], "type": "text", "content": " is a 2D integer array with dimension as (nmate,2) holding the failure criterion number and number of strength constants for each material. strength_constants is a 2D real array with dimension as (nmate,9) holding the strength constants for each material. Nload is an integer indicating the number of load cases."}]}], "index": 0}, {"bbox": [88, 535, 333, 549], "type": "text", "lines": [{"bbox": [88, 535, 333, 549], "spans": [{"bbox": [88, 535, 333, 549], "type": "text", "content": "The following variables are passed from Recovery"}]}], "index": 1}, {"bbox": [105, 552, 523, 713], "type": "text", "lines": [{"bbox": [105, 552, 523, 713], "spans": [{"bbox": [105, 552, 523, 713], "type": "text", "content": "disp "}, {"bbox": [105, 552, 523, 713], "type": "inline_equation", "content": "3D\\_ F"}, {"bbox": [105, 552, 523, 713], "type": "text", "content": " is a 2D real array with dimension as (nnode,5) storing the recovered 3D displacements. "}, {"bbox": [105, 552, 523, 713], "type": "inline_equation", "content": "k\\_ F"}, {"bbox": [105, 552, 523, 713], "type": "text", "content": " is an integer number used to indicate the total number of Gauss points we have recovered the 3D stresses and strains. "}, {"bbox": [105, 552, 523, 713], "type": "inline_equation", "content": "nd\\_ F"}, {"bbox": [105, 552, 523, 713], "type": "text", "content": " is an integer number used to indicate the total number of nodes we have recovered the 3D stresses and strains. ss "}, {"bbox": [105, 552, 523, 713], "type": "inline_equation", "content": "F"}, {"bbox": [105, 552, 523, 713], "type": "text", "content": " is a 2D real array with dimension as (nelem\\*MAX_NODE_ELEM,26) storing the recovered 3D stresses and strains at Gauss points, where ss_F(:,1:2) denotes the position, ss_F(:,3:8) denotes the strains in beam coordinate system, ss_F(:,9:14) denotes the stresses in beam coordinate system, ss_F(:,15:20) denotes the strains in material coordinate system, ss_F(:,21:26) denotes the stresses in"}]}], "index": 2}], "discarded_blocks": [], "page_size": [612, 792], "page_idx": 4}, {"para_blocks": [{"bbox": [121, 73, 452, 87], "type": "text", "lines": [{"bbox": [121, 73, 452, 87], "spans": [{"bbox": [121, 73, 452, 87], "type": "text", "content": "material coordinate system. Only the first "}, {"bbox": [121, 73, 452, 87], "type": "inline_equation", "content": "k\\_ F"}, {"bbox": [121, 73, 452, 87], "type": "text", "content": " rows are meaningful."}]}], "index": 0}, {"bbox": [105, 91, 523, 187], "type": "text", "lines": [{"bbox": [105, 91, 523, 187], "spans": [{"bbox": [105, 91, 523, 187], "type": "text", "content": "ss_nd_F is a 2D real array with dimension as (nelem\\*MAX_NODE_ELEM,26) storing the recovered 3D stresses and strains at nodal points, where ss_nd_F(:,1:2) denotes the position, ss_nd_F (:,3:8) denotes the strains in beam coordinate system, ss_nd_F (:,9:14) denotes the stresses in beam coordinate system, ss_nd_F(:,15:20) denotes the strains in material coordinate system, ss_nd_F(:,21:26) denotes the stresses in material coordinate system. Only the first nd_F rows are meaningful."}]}], "index": 1}, {"bbox": [105, 190, 523, 286], "type": "text", "lines": [{"bbox": [105, 190, 523, 286], "spans": [{"bbox": [105, 190, 523, 286], "type": "text", "content": "ss_elem is a 2D real array with dimension as (<PERSON><PERSON>,24) storing the average of recovered 3D stresses and strains of all the Gauss points within one element, where ss_elem(:,1:6) denotes the strains in beam coordinate system, ss_elem (:,7:12) denotes the stresses in beam coordinate system, ss_elem(:,13:18) denotes the strains in material coordinate system, ss_elem(:,19:24) denotes the stresses in material coordinate system. This array can be used to facilitate contour plot for visualization."}]}], "index": 2}, {"bbox": [105, 289, 523, 419], "type": "text", "lines": [{"bbox": [105, 289, 523, 419], "spans": [{"bbox": [105, 289, 523, 419], "type": "text", "content": "- "}, {"bbox": [105, 289, 523, 419], "type": "inline_equation", "content": "f_i\\_ pos"}, {"bbox": [105, 289, 523, 419], "type": "text", "content": " is a 2D real array with dimension as (nelem,2) storing the failure index and strength ratio for each element.- mode_pos is a 1D character array with dimension as (nelem) storing the failure mode for each element.- str_comp is a 2D real array with dimension as (nelem,6) storing the strength ratio corresponding to each stress components for each element with failure criterion 1 or 2 for composites. If the failure criterion is 5, only the first two elements str_comp(nelem,2) are used storing the strength ratios for the fiber failure or matrix failure for each element with failure criterion 5."}]}], "index": 3}, {"bbox": [89, 434, 240, 449], "type": "title", "lines": [{"bbox": [89, 434, 240, 449], "spans": [{"bbox": [89, 434, 240, 449], "type": "text", "content": "6. Standalone Application"}]}], "index": 4, "level": 2, "line_avg_height": 19}, {"bbox": [88, 463, 523, 587], "type": "text", "lines": [{"bbox": [88, 463, 523, 587], "spans": [{"bbox": [88, 463, 523, 587], "type": "text", "content": "The standard release includes the standalone application including VABSLib.dll and the files needed to compile the standalone application including main.f90, VABSIO.f90, and GlobalDataFunE.f90. Interfaces are provided in main.f90 so that the two subroutines can be called properly. VABSIO.f90 defines/inputs/outputs all the arguments needed to pass to/from the two subroutines. GlobalDataFunE.f90 defines some global constants and functions needed for VABSIO.f90. If you are familiar with the Fortran language, these files might be able to facilitate your development. The developers are also free to modify the source codes to add more capabilities which are designed to take advantage of VABS available through VABSLib.dll."}]}], "index": 5}, {"bbox": [88, 601, 520, 656], "type": "text", "lines": [{"bbox": [88, 601, 520, 656], "spans": [{"bbox": [88, 601, 520, 656], "type": "text", "content": "If the variables as explained previously are defined correctly, the remaining task for the developer to integrate VABS is to provide the interface necessary for calling the two subroutines. For example, for Fortran 90/95, the two needed interfaces are provided in main.f90."}]}], "index": 6}], "discarded_blocks": [], "page_size": [612, 792], "page_idx": 5}], "_backend": "vlm", "_version_name": "2.1.10"}